# frozen_string_literal: true

class Api::V1::AccessController < ApplicationController
  skip_before_action :forbid_access_if_lacking_session
  skip_authorization_check

  before_action :set_user

  def check
    render json: { canvas_id: @user.canvas_id, is_k5_student: @user.k5_student?, is_learning_coach: @user.k5_learning_coach? }, status: :ok
  end

  private

  def set_user
    @user = User.find_by!(canvas_id: params[:id])
  rescue ActiveRecord::RecordNotFound
    head :not_found
  end
end
