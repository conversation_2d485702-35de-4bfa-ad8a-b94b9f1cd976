# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CourseProgress, type: :model do
  let(:course_progress) { create(:course_progress) }

  describe 'validations' do
    subject { course_progress }

    it { should validate_presence_of(:canvas_course_id) }
    it { should validate_presence_of(:canvas_user_id) }
    it { should validate_uniqueness_of(:canvas_user_id).scoped_to(:canvas_course_id) }
  end

  describe 'Associations' do
    it { should belong_to(:course) }
    it { should belong_to(:user) }
  end
end
