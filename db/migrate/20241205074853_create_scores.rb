class CreateScores < ActiveRecord::Migration[7.0]
  def change
    create_table :scores do |t|
      t.bigint :canvas_enrollment_id, null: false
      t.float :current_score
      t.float :unposted_current_score
      t.float :final_score
      t.string :current_letter_grade
      t.string :unposted_letter_grade
      t.string :final_letter_grade
      t.string :override_score
      t.string :workflow_state, null: false

      t.timestamps
    end
    add_index :scores, :canvas_enrollment_id, unique: true
  end
end
