import '@/application.css'
import { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router, Routes } from 'react-router-dom'

import { View } from '@instructure/ui-view';
import { Tabs } from '@instructure/ui-tabs';
import { Alert } from '@instructure/ui-alerts';
import { Text } from '@instructure/ui-text';
import { Link } from '@instructure/ui-link';

import * as API from "../../utils/api";
import CoursesTab from './CoursesTab';
import ResourcesTab from './ResourcesTab';
import AgendaTab from './AgendaTab';
import AnnouncementsTab from './AnnouncementsTab';
import TeachersTab from './TeachersTab';
import { TextWithBadgeCount } from '../../shared/components/UtilUI';
// import CourseProgress from '../courses/CourseProgress';
import CourseNavigation from '../courses/CourseNavigation';


const DashboardBody = (props) => {
  const { userId, isK5Student = false, setCurrentView, currentTheme, themeAssets } = props;
  const [homepage, setHomepage] = useState(null);
  const [selectedTab, setSelectedTab] = useState();
  const [announcements, setAnnouncements] = useState([]);
  const [showNotificationTooltip, setShowNotificationTooltip] = useState(false);

  useEffect(() => {
    if (userId) {
      setHomepage(null);
      getUserConfig();
    }
  }, [userId]);

  useEffect(() => {
    if (isK5Student && userId) {
      fetchAnnouncements();
    }
  }, [isK5Student, userId]);

  useEffect(() => {
    const shouldShow = isK5Student &&
                   (window.ENV.user_config?.show_notification_tooltip ?? true) &&
                   (selectedTab === 'course_view');

    setShowNotificationTooltip(shouldShow);
  }, [selectedTab]);

  const getUserConfig = () => {
    API.getUserDashboardConfig(userId)
      .then((response) => response.data)
      .then((response) => {
        setHomepage(response.home_page);
        setSelectedTab((window.ENV.assignment_course_id && window.ENV.assignment_course_id != '$Canvas.course.id')? 'course_view' : response.home_page);
           if (isK5Student) {
             setCurrentView((window.ENV.assignment_course_id && window.ENV.assignment_course_id != '$Canvas.course.id')? 'course_view' : response.home_page);
            }
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const fetchAnnouncements = () => {
    API.getAnnouncements(userId)
      .then(response => response.data)
      .then(data => {
        setAnnouncements(data);
      })
      .catch(error => {
        console.error('Failed to load announcements', error);
      });
  };

  const handleAnnouncementsChange = (updatedAnnouncements) => {
    setAnnouncements(updatedAnnouncements);
  };

  const handleTabChange = (data) => {
    setSelectedTab(data.id)
       if (isK5Student) {
        setCurrentView(data.id);
      }
  }

  const renderAnnouncementsTitle = () => {
    if (selectedTab === 'announcements') {
      return renderTabTitle('Announcements')
    }

    return (<View
        as="div"
        className="header-text"
      >
        <TextWithBadgeCount title='Announcements' count={announcements.length} variant='primary' />
      </View>
    );
  };

  const handleDismissNotification = () => {
    const userConfigData = {
      show_notification_tooltip: false,
      skip_canvas_update: true
    };

    API.updateUserDashboardConfig(userId, window.ENV.user_config?.id || 0, userConfigData)
      .then(() => {
        window.ENV.user_config.show_notification_tooltip = false;
        setShowNotificationTooltip(false);
      })
      .catch(error => {
        console.error('Failed to dismiss notification', error);
      });
  };

  const renderNotificationTooltip = () => {
    const canvasUrl = window.location.ancestorOrigins?.[0] || window.ENV?.canvas_url;
    const notificationPreferencesUrl = canvasUrl ? `${canvasUrl}/profile/communication` : '#';

    return (
      <View
        as="div"
        className='notification-tooltip'
        position="fixed"
        stacking="topmost"
        style={{
          left: '50%',
          transform: 'translateX(-50%)'
        }}
      >
        <Alert
          variant="info"
          renderCloseButtonLabel="Dismiss notification"
          onDismiss={handleDismissNotification}
        >
          <View as="div" textAlign="start" lineHeight="condensed">
            <Text weight='bold'>Notifications:</Text> Tell us how and when you would like to be notified of events in Canvas.
            <br />
            <Link href={notificationPreferencesUrl} target="_parent" themeOverride={{focusOutlineStyle: 'none'}}>
              Set Canvas Notification Preferences
            </Link>
          </View>
        </Alert>
      </View>
    );
  };

  const renderTabTitle = (label) => {
    return (
      <View
        as="div"
        padding="x-small none"
        className="header-text"
      >
        {label}
      </View>
    );
  };

  return (
    <View as="div">
      {showNotificationTooltip && renderNotificationTooltip()}
      { homepage &&
        <View
          as='div'
          padding='0 medium 0 large'
          className='dashboard-body-container'
          height='100%'
          width='100%'
          {...props}
          themeOverride={{
            fontFamily: '__Balsamiq Sans_5'
          }}
        >

          <Tabs
            margin='small 0'
            padding='0'
            onRequestTabChange={(_, data) => handleTabChange(data)}
            themeOverride={{
              // defaultBackground: '#DFEBFB'
              defaultBackground: 'transparent !important',
            }} >
            <Tabs.Panel
              key='course_view'
              id='course_view'
              renderTitle={renderTabTitle('Courses')}
              textAlign='center'
              padding='xx-small 0 0 0'
              isSelected={selectedTab === 'course_view'}
              themeOverride={{background: '#DFEBFB'}}
            >
              <Router>
                <Routes>
                  <Route path="/" element={<CoursesTab studentId={userId } />} />
                  <Route path=":studentId/course/:courseId" element={<CourseNavigation />} />
                </Routes>
              </Router>
            </Tabs.Panel>
            <Tabs.Panel
              key='agenda_view'
              id='agenda_view'
              renderTitle={renderTabTitle('Agenda')}
              textAlign='center'
              padding='xx-small 0 0 0'
              isSelected={selectedTab === 'agenda_view'}
              themeOverride={{background: '#DFEBFB'}}
            >
              <Router>
                <Routes>
                  <Route path="/" element={<AgendaTab studentId={userId} isK5Student={isK5Student} currentTheme={currentTheme} themeAssets={themeAssets} />} />
                </Routes>
              </Router>
            </Tabs.Panel>
            <Tabs.Panel
              key='resources'
              id='resources'
              renderTitle={renderTabTitle('Resources')}
              textAlign='center'
              padding='xx-small 0 0 0'
              isSelected={selectedTab === 'resources'}
              themeOverride={{background: '#DFEBFB'}}
            >
              <ResourcesTab />
            </Tabs.Panel>
            {isK5Student && (
              <Tabs.Panel
                key='announcements'
                id='announcements'
                renderTitle={renderAnnouncementsTitle()}
                textAlign='center'
                padding='xx-small 0 0 0'
                isSelected={selectedTab === 'announcements'}
                themeOverride={{ background: '#DFEBFB' }}
              >
                <AnnouncementsTab
                  studentId={userId}
                  announcements={announcements}
                  onAnnouncementsChange={handleAnnouncementsChange}
                />
              </Tabs.Panel>
            )}
            <Tabs.Panel
              key='teachers'
              id='teachers'
              renderTitle={renderTabTitle('Teachers')}
              textAlign='center'
              padding='xx-small 0 0 0'
              isSelected={selectedTab === 'teachers'}
              themeOverride={{background: '#DFEBFB'}}
            >
              <TeachersTab studentId={userId}/>
            </Tabs.Panel>
          </Tabs>
        </View>
      }
    </View>
  )
}

export default DashboardBody
