# frozen_string_literal: true

# #
# AUTO GENERATED LIVE EVENT
# This was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#
# LiveEvent message formats can be found at https://canvas.instructure.com/doc/api/file.live_events.html
# In the general case, LiveEvent message content should not be trusted - it is highly possible that events may
# arrive out of order. Most of the CanvasSync LiveEvent templates solve this by always fetching the object from the API.
#

module LiveEvents
  class ModuleItemEvent < CanvasSync::LiveEvents::BaseHandler
    def process
      # Ensure context module exists and is synced first
      context_module = ContextModule.find_or_initialize_by(
        canvas_id: local_canvas_id(payload[:module_id]),
        canvas_context_id: local_canvas_id(payload[:context_id]),
        canvas_context_type: payload[:context_type]
      )
      context_module.sync_from_api

      Rails.logger.info("#{event_info} Context module synced: module_id=#{context_module.canvas_id}, " \
                        "context_id=#{context_module.canvas_context_id}")

      context_module_item = ContextModuleItem.find_or_initialize_by(canvas_id: payload['module_item_id'])
      context_module_item.sync_from_api

      Rails.logger.info("#{event_info} Module item synced: module_item_id=#{context_module_item.canvas_id}, " \
                        "module_id=#{payload['module_id']}")
    end

    private

    # Live events will use a canvas global ID (cross shard) for any ID's provided. This method will return the local ID.
    def local_canvas_id(id)
      id.to_i % 10_000_000_000_000
    end

    def event_info
      @event_info ||= "[Org: #{current_organization.name}] [ModuleItemEvent]"
    end
  end
end
