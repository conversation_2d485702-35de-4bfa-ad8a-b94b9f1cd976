import { useEffect, useState } from 'react';

import { Modal } from '@instructure/ui-modal';
import { View } from '@instructure/ui-view';
import { <PERSON><PERSON>, CloseButton } from '@instructure/ui-buttons';
import { Heading } from "@instructure/ui-heading";
import { Flex } from '@instructure/ui-flex';
import { RadioInput, RadioInputGroup } from '@instructure/ui-radio-input';
import { Text } from '@instructure/ui-text';

import moment from 'moment-timezone';

import InstSelect from '../../shared/components/InstSelect';
import GroupSelect from '../../shared/components/GroupSelect';
import * as API from "../../utils/api";
import * as GlobalConstant from '../../utils/constants';

const UserConfigModal = (props) => {
  const { isModalOpen, toggleModal, userId } = props;

  const [state, setState] = useState({
    confId: null,
    language: "",
    timezone: "",
    homepage: "",
  });

  useEffect(() => {
    if (userId) {
      getUserConfig();
    }
  }, [userId]);

  const getUserConfig = () => {
    const canvas_user_locale = window.ENV.canvas_user_locale;
    const canvas_user_timezone = window.ENV.canvas_user_timezone;

    API.getUserDashboardConfig(userId)
      .then((response) => response.data)
      .then((response) => {
        const userConfigs = {
          confId: response.id,
          homepage: response.home_page,
          language: response.language || canvas_user_locale,
          timezone: response.timezone || canvas_user_timezone,
        };
        setState(userConfigs);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const closeModal = () => {
    toggleModal()
  };

  const renderLanguages = () => {
    const obj = GlobalConstant.CANVAS_LOCALES.find(ob => { if(ob.id == state.language) return ob })
    return(
      <View>
        <View as="div"><Text weight="bold">Languages</Text></View>
        <View as="div" padding="small 0">{obj.label}</View>
      </View>
    )
  }

  const renderTimeZones = () => {
    return(
      <View>
        <View>
          <View as="div"><Text weight="bold">Time Zone</Text></View>
          <View as="div" padding="small 0">{state.timezone || ''}</View>
        </View>
      </View>
    )
  }

  const renderHomePages = () => {
    const obj = GlobalConstant.HOME_PAGES.find(ob => { if(ob.value == state.homepage) return ob })
    return (
        <View>
          <View>
            <View as="div"><Text weight="bold">Default Dashboard Homepage</Text></View>
            <View as="div" padding="small 0">{obj.label}</View>
          </View>
        </View>
    )
  }

  return (
    <View>
      <Modal
        open={isModalOpen}
        onDismiss={(event) => closeModal()}
        size="medium"
        label="Dashboard Settings"
        shouldCloseOnDocumentClick
      >
        <Modal.Header>
          <CloseButton
            placement="end"
            offset="small"
            onClick={(event) => closeModal(event)}
            screenReaderLabel="Close"
          />
          <Heading level="h2">Dashboard Settings</Heading>
        </Modal.Header>
        <Modal.Body>
          <View>
            <Flex direction="column">
              <Flex.Item padding="small none">
                { state && state.language && renderLanguages() }
              </Flex.Item>
              <Flex.Item padding="small none">
                { state && state.timezone && renderTimeZones() }
              </Flex.Item>
              <Flex.Item padding="small none">
                { state && state.homepage && renderHomePages() }
              </Flex.Item>
            </Flex>
          </View>
        </Modal.Body>
        <Modal.Footer>
          <Button 
            margin="0 x-small 0 0"
            onClick={(event) => closeModal(event)}
          >
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </View>
  )
}

export default UserConfigModal
