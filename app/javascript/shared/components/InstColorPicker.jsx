import { useEffect, useState } from 'react';

import { ColorPicker, ColorMixer, ColorPreset, ColorContrast, ColorIndicator } from '@instructure/ui-color-picker';
import { Button } from '@instructure/ui-buttons';

const InstColorPicker = (props) => {
  const { schemeId, colorCode, handleChange } = props;
  const [value, setValue] = useState(colorCode || '')

  useEffect(() => {
    if (colorCode != value) {
      setValue(colorCode);
    }
  }, [colorCode]);

  const onChange = (val) => {
    setValue(val)
    handleChange(schemeId, val);
  }

  const renderPopoverContent = (value, onChange, handleAdd, handleClose) => (
    <div>
      <div style={{ padding: '20px' }}>
        <ColorMixer
          value={value}
          onChange={onChange}
          rgbRedInputScreenReaderLabel="Input field for red"
          rgbGreenInputScreenReaderLabel="Input field for green"
          rgbBlueInputScreenReaderLabel="Input field for blue"
          rgbAlphaInputScreenReaderLabel="Input field for alpha"
          colorSliderNavigationExplanationScreenReaderLabel={`You are on a color slider. To navigate the slider left or right, use the 'A' and 'D' buttons respectively`}
          alphaSliderNavigationExplanationScreenReaderLabel={`You are on an alpha slider. To navigate the slider left or right, use the 'A' and 'D' buttons respectively`}
          colorPaletteNavigationExplanationScreenReaderLabel={`You are on a color palette. To navigate on the palette up, left, down or right, use the 'W', 'A', 'S' and 'D' buttons respectively`}
        />
        <div
          style={{
            borderTop: 'solid',
            borderWidth: '1px',
            borderColor: '#C7CDD1',
            margin: '20px 0 20px 0'
          }}
        />
        <ColorIndicator color={value} />
      </div>
      <div
        style={{
          backgroundColor: '#F5F5F5',
          display: 'flex',
          flexDirection: 'row-reverse',
          padding: '7px',
          borderTop: 'solid 1px #C7CDD1'
        }}
      >
        <Button onClick={handleAdd} color="primary" margin="xx-small">
          Add
        </Button>
        <Button onClick={handleClose} color="secondary" margin="xx-small">
          Close
        </Button>
      </div>
    </div>
  )

  return (
    <div>
      <ColorPicker
        value={value}
        onChange={onChange}
        label=""
        placeholderText="Enter HEX"
        popoverButtonScreenReaderLabel="Open color mixer popover"
        width="35rem"
      >
        { renderPopoverContent }
      </ColorPicker>
    </div>
  )
}

export default InstColorPicker
