# frozen_string_literal: true

# #
# AUTO GENERATED LIVE EVENT
# This was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#
# LiveEvent message formats can be found at https://canvas.instructure.com/doc/api/file.live_events.html
# In the general case, LiveEvent message content should not be trusted - it is highly possible that events may
# arrive out of order. Most of the CanvasSync LiveEvent templates solve this by always fetching the object from the API.
#

module LiveEvents
  class AssignmentEvent < CanvasSync::LiveEvents::BaseHandler
    def process
      canvas_assignment_id = local_canvas_id(payload[:assignment_id])
      assignment = Assignment.where(canvas_id: canvas_assignment_id).first_or_initialize
      assignment.canvas_context_id = canvas_course_id
      assignment.sync_from_api
    end

    private

    # This could be nil if the context type for this assignment is not 'Course' of if a local course was not found
    def canvas_course_id
      payload[:context_type] == 'Course' ? local_canvas_id(payload[:context_id]) : nil
    end

    # This could be nil if the context type for this assignment is not 'Course' of if a local course was not found
    def course
      @course ||= begin
        course = Course.where(canvas_id: canvas_course_id).first_or_initialize
        course.sync_from_api unless course.persisted?
        course
      end
    end

    # Live events will use a canvas global ID (cross shard) for any ID's provided. This method will return the local ID.
    def local_canvas_id(id)
      id.to_i % 10_000_000_000_000
    end
  end
end
