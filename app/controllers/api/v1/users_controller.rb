# frozen_string_literal: true

class Api::V1::UsersController < ApplicationController
  skip_before_action :forbid_access_if_lacking_session
  skip_authorization_check
  before_action :set_user

  def show; end

  def dashboard_students
    authorize! :read, User
    @students = students_from_all_shards
  end

  private

  def students_from_all_shards
    student_ids = []

    @user.against_shards do |shard_user|
      User.where(canvas_id: shard_user.observee_ids).find_each do |student|
        id = student.home_organization.name == current_organization_name ? student.primary_record.canvas_id : student.sharded_canvas_id
        student_ids << id
      end
    end

    User.where(canvas_id: student_ids).sort_by(&:sortable_name)
  end

  def set_user
    @user = User.find_by(canvas_id: params[:id])
  end

  def current_organization_name
    @current_organization_name ||= current_organization.name
  end
end
