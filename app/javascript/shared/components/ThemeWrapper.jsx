import { useTheme } from '../contexts/ThemeContext';
import { getThemeColors } from '@/utils/themeHelper';

const ThemeWrapper = ({ children, activePage = 'course', enableBackground = true, enableElements = true, className = '', isLearningCoach = false, ...props }) => {
  const { currentTheme: contextTheme, themeAssets } = useTheme();
  const currentTheme = isLearningCoach ? 'no_theme' : contextTheme;
  const backgroundImage = themeAssets?.background?.[activePage];

  const getThemeStyles = () => {
    if (currentTheme === 'no_theme') {
      const colors = getThemeColors('no_theme');
      return {
        backgroundColor: colors?.background || '#F5F5F5',
        minHeight: '100vh',
        width: '100vw',
        margin: 0,
        padding: 0,
        overflowX: 'hidden'
      };
    }
    if (!enableBackground) return {};

    // Use CSS for responsive background-attachment instead of inline styles
    // This allows better control via media queries
    return {
      backgroundColor: themeAssets?.colors?.background,
      backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center bottom',
      backgroundSize: currentTheme === 'space' ? 'cover' : 'contain',
      minHeight: '100vh',
      width: '100vw',
      margin: 0,
      padding: 0,
      overflowX: 'hidden'
    };
  };

  const getThemeClassName = () => {
    const classes = ['theme-wrapper', `theme-${currentTheme}`];
    if (className) {
      classes.push(className);
    }
    return classes.join(' ');
  };

  return (
    <div
      className={getThemeClassName()}
      style={getThemeStyles()}
      data-theme={currentTheme}
      {...props}
    >
      {children}
    </div>
  );
};

export default ThemeWrapper;
