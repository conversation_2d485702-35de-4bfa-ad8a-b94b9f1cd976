# frozen_string_literal: true

class Ability
  include CanCan::Ability
  include CanvasSync::Concerns::AbilityHelper
  include PandaPal::Concerns::AbilityHelper

  def initialize(user, rails_session:, panda_session:)
    # Define abilities for the user here. For example:
    #
    #   return unless user.present?
    #   can :read, :all
    #   return unless user.admin?
    #   can :manage, :all
    #
    # The first argument to `can` is the action you are giving the user
    # permission to do.
    # If you pass :manage it will apply to every action. Other common actions
    # here are :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on.
    # If you pass :all it will apply to every resource. Otherwise pass a Ruby
    # class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the
    # objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, published: true
    #
    # See the wiki for details:
    # https://github.com/CanCanCommunity/cancancan/blob/develop/docs/define_check_abilities.md

    @user = user
    @panda_pal_session = panda_session
    @rails_session = rails_session

    if user_is_account_admin?
      can :manage, :global_settings

      can :manage, AccountGradingSchemeColor
      can :launch_from, :account
    end

    return unless can_access_tool?

    can :launch_from, :course
    can :launch_from, :global

    can :read, User if learning_coach?
  end

  def canvas_roles
    @canvas_roles ||= Role.for_labels(panda_pal_session.canvas_role_labels, launch_account)
  end

  def user_is_account_admin?
    @user_is_account_admin ||= canvas_account_roles.pluck(:base_role_type).any?('AccountMembership')
  end

  def can_access_tool?
    # TODO: add all the access permissions logic
    return true if learning_coach? || k5_student?

    false
  end

  def learning_coach?
    @user.k5_learning_coach?
  end

  def k5_student?
    @user.k5_student?
  end
end
