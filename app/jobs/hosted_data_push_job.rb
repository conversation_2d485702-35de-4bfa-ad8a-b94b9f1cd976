# frozen_string_literal: true

class HostedDataPushJob < ApplicationJob
  queue_as :default

  SCHEMA = InstDataShipper::SchemaBuilder.build do
    table('dashboard_course_data', model: Course) do
      source :local_table

      column :id
      column :canvas_id
      column :sis_id, :'varchar(128)', from: ->(obj) { obj.sis_id.to_s }
      column :grade_level, :'varchar(128)', from: ->(obj) { obj.grade_level.to_s }
    end
  end

  Dumper = InstDataShipper::Dumper.define(schema: SCHEMA, include: [InstDataShipper::DataSources::LocalTables])

  def perform
    destinations = current_organization.settings[:data_shipper_destinations]
    return if destinations.blank?

    Dumper.perform_dump(destinations)
  end
end
