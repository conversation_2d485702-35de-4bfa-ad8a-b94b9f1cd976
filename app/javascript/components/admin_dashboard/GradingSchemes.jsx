import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { Button } from '@instructure/ui-buttons';
import { IconSaveLine } from '@instructure/ui-icons';

import SchemeItems from './SchemeItems';


const GradingSchemes = (props) => {
  const { schemeType, records, accountId } = props;
  const renderSchemes = () => {
    if (records) {
      return records.map(row => {
        return (
          <Flex.Item key={'row_'+row['id']} padding="small none">
            <SchemeItems item={row} accountId={accountId} />
          </Flex.Item>
        )
      })
    }   
  }

  return (
    <View as="div">
      <Flex direction='column'>
        { records && renderSchemes() }
      </Flex>
    </View>
  )
}

export default GradingSchemes
