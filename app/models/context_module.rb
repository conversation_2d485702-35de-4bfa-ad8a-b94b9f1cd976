# frozen_string_literal: true

# # #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#

# A ContextModule is the same as a Canvas Module. They're called ContextModules for 2 reasons:
#   1 - Module is a reserved word in Rails and you can't call a model a Module
#   2 - Canvas calls them ContextModules
class ContextModule < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  # include CanvasSync::Concerns::LiveEventSync

  canvas_sync_features :defaults

  belongs_to :context, polymorphic: true, optional: true, primary_key: :canvas_id, foreign_key: :canvas_context_id, foreign_type: :canvas_context_type
  belongs_to :course, primary_key: :canvas_id, foreign_key: :canvas_context_id
  has_many :context_module_items, primary_key: :canvas_id, foreign_key: :canvas_context_module_id
  has_many :context_module_progressions, primary_key: :canvas_id, foreign_key: :canvas_module_id
  has_many :assignments, through: :context_module_items

  api_syncable({
                 canvas_id: :canvas_context_module_id,
                 canvas_context_id: :canvas_context_id,
                 canvas_context_type: :canvas_context_type,
                 position: :position,
                 name: :name,
                 workflow_state: :workflow_state,
                 unlock_at: :unlock_at,
                 completion_requirements: :completion_requirements,
                 prerequisites: :prerequisites,
                 requirement_count: :requirement_count
               }, lambda { |api|
                    # TODO: Not sure if we need all_pages! here
                    course_modules = api.course_modules(canvas_context_id, { include: ['items'] }) # avoids having 2 API calls for modules as we reuse this data
                    context_module = course_modules&.detect { |course_module| course_module['id'] == canvas_id }

                    if context_module.nil? # no context_module here means it is deleted in Canvas
                      self.workflow_state = :deleted

                      # HACK: Replaced `return self` with return attributes as it is expecting a hash to be returned, not an ActiveRecord object.
                      return attributes
                    end

                    # The ContextModule model in Canvas has the 'completion_requirements' and 'prerequisites' fields that are not returned directly from the API
                    # We need to compute them here based on data that we receive from course_modules in the API
                    context_module['prerequisites'] = course_modules.map do |course_module|
                      next unless context_module['prerequisite_module_ids']&.include?(course_module['id'])

                      {
                        id: course_module['id'],
                        name: course_module['name'],
                        type: 'context_module'
                      }.with_indifferent_access
                    end.compact

                    context_module['completion_requirements'] = context_module['items']&.map { |item| { id: item['id'] }.merge(item['completion_requirement']) }&.compact
                    context_module
                  })
end
