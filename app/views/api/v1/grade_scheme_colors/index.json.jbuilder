# frozen_string_literal: true

@account_grading_schemes.each do |scheme_type, records|
  max_value = 1

  json.set! scheme_type, records do |gsc|
    json.id                   gsc.id
    json.canvas_account_id    gsc.canvas_account_id
    json.scheme_color_type    gsc.scheme_color_type
    json.name                 gsc.name
    json.range_value          gsc.range_value
    json.color_code           gsc.color_code
    json.default_color_code   gsc.default_color_code

    json.range_limit gsc.range_limit(gsc.range_value, max_value)
    max_value = gsc.range_value.to_d
  end
end
