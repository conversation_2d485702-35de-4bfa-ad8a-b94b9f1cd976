# frozen_string_literal: true

class GenerateAccountGradingSchemeColorsJob < CanvasSync::Job
  queue_as :critical

  def perform(_options = {})
    Account.where.missing(:account_grading_scheme_colors).find_each do |account|
      create_grading_scheme_color(account.canvas_id)
    end
  rescue StandardError => e
    Rails.logger.error "Error: #{e.message}\n#{e.backtrace.join("\n")}"
  end

  def create_grading_scheme_color(canvas_account_id)
    grading_schemes = default_standard_grading_scheme.map do |scheme|
      { canvas_account_id: canvas_account_id,
        scheme_color_type: :standard,
        name: scheme['name'].to_s,
        range_value: scheme['value'],
        default_color_code: scheme['default_color_code'] }
    end + default_mastery_grading_scheme.map do |scheme|
      { canvas_account_id: canvas_account_id,
        scheme_color_type: :mastery,
        name: scheme['name'].to_s,
        range_value: scheme['value'],
        default_color_code: scheme['default_color_code'] }
    end

    AccountGradingSchemeColor.insert_all(grading_schemes) if grading_schemes.any?
  end
end
