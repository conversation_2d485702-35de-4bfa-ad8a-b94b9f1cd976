# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateCourses < ActiveRecord::Migration[5.1]
  def change
    create_table :courses do |t|
      t.bigint :canvas_id, null: false
      t.string :sis_id
      t.string :course_code
      t.string :name
      t.string :workflow_state
      t.integer :canvas_account_id
      t.integer :canvas_term_id
      t.datetime :start_at
      t.datetime :end_at
      t.bigint :grading_standard_id

      t.timestamps
    end

    add_index :courses, :canvas_id, unique: true
  end
end
