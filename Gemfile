# frozen_string_literal: true

source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.1.2'

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem 'rails', '~> 7.0.8', '>= *******'

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem 'aws-sdk-s3'
gem 'cancancan', '~> 3.5'
gem 'health_check'
gem 'importmap-rails'
gem 'jbuilder'
gem 'puma', '~> 5.0'
gem 'redis', '~> 5.1.0'
gem 'sprockets-rails'
gem 'stimulus-rails'
gem 'turbo-rails'

gem 'bearcat', '~> 1.5.31'
gem 'canvas_sync', '~> 0.22.14'
gem 'lti_roles', '~> 0.0.4'
gem 'miscellany', '~> 0.1.22'
gem 'panda_pal', '~> 5.13.2'
gem 'paul_bunyan'

gem 'rack-cors'
gem 'react-rails', '~> 3.2'
gem 'ros-apartment-sidekiq', '~> 1.2', require: 'apartment-sidekiq'
gem 'shakapacker', '~> 7.2'
gem 'sidekiq', '~> 7.2'
gem 'sidekiq-failures'
gem 'sidekiq-scheduler', '~> 5.0'

gem 'sentry-rails'
gem 'sentry-ruby'
gem 'sentry-sidekiq'
gem 'stackprof'

gem 'pg', '~> 1.5.4'

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', require: false
gem 'mutex_m', '~> 0.2.0'

# Use Sass to process CSS
# gem "sassc-rails"

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# SFTP
gem 'bcrypt_pbkdf', '~> 1.1'
gem 'ed25519', '~> 1.3'
gem 'net-sftp', '~> 4.0'

# Faraday extra dependency
gem 'faraday-follow_redirects', '~> 0.3.0'

# InstDataShipper - Facilitate easy upload of LTI datasets to Instructure Hosted Data
gem 'inst_data_shipper', '~> 0.2.6'

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem 'brakeman', require: false
  gem 'byebug', platforms: [:mri, :mingw, :x64_mingw]
  gem 'debug', platforms: [:mri, :mingw, :x64_mingw]
  gem 'factory_bot_rails'
  gem 'faker'
  gem 'gergich', require: false
  gem 'pry'
  gem 'rspec-rails'
  gem 'rubocop', '~> 1.67'
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem 'web-console'

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem 'capybara'
  gem 'selenium-webdriver'
  gem 'shoulda'
  gem 'shoulda-matchers'
end
