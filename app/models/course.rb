# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Course < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  canvas_sync_features :defaults

  # include CanvasSync::Concerns::LiveEventSync
  # after_process_live_event do
  #   if account.nil?
  #     acc = Account.new(canvas_id: canvas_account_id)
  #     acc.sync_from_api
  #   end
  # end

  validates :canvas_id, uniqueness: true, presence: true
  belongs_to :term, foreign_key: :canvas_term_id, primary_key: :canvas_id, optional: true
  belongs_to :account, foreign_key: :canvas_account_id, primary_key: :canvas_id, optional: true
  has_many :enrollments, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :sections, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :assignments, as: :context, primary_key: :canvas_id, foreign_key: :canvas_context_id, foreign_type: :canvas_context_type
  has_many :submissions, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :assignment_groups, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :groups, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :rubrics, as: :context, primary_key: :canvas_id, foreign_key: :canvas_context_id, foreign_type: :canvas_context_type
  has_many :course_progresses, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :context_modules, as: :context, primary_key: :canvas_id, foreign_key: :canvas_context_id, foreign_type: :canvas_context_type
  has_one :grading_scheme, primary_key: :canvas_id, foreign_key: :canvas_course_id, dependent: :destroy

  api_syncable({
                 sis_id: :sis_course_id,
                 course_code: :course_code,
                 name: :name,
                 workflow_state: :workflow_state,
                 canvas_term_id: :enrollment_term_id,
                 canvas_account_id: :account_id,
                 start_at: :start_at,
                 end_at: :end_at
               }, ->(api) { api.course(canvas_id) })

  scope :selectable, -> { where(workflow_state: %w[active completed]) }
  scope :not_deleted, -> { where("courses.workflow_state <> 'deleted'") }

  scope :with_only_k5_students, lambda {
    not_deleted
      .where(canvas_id: Enrollment.only_with_k5_students.map(&:canvas_course_id))
  }

  def with_module_items
    context_modules.includes(context_module_items: { assignment: [:submissions] })
                   .where(context_module_items: { canvas_id: module_requiment_ids })
                   .where(context_modules: { workflow_state: 'active' })
                   .where(context_module_items: { workflow_state: 'active' })
  end

  def module_requiment_ids
    context_modules.map { |m| m.completion_requirements.map { |item| item['id'] } }.flatten
  end
end
