# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ExternalResource, type: :model do
  let(:external_resource) { create(:external_resource, position: 1) }

  describe 'validations' do
    subject { external_resource }

    it { should validate_presence_of(:base_url) }
    it { should validate_presence_of(:position) }
  end

  describe '#banner' do
    subject { external_resource.banner }

    it { is_expected.to be_an_instance_of(ActiveStorage::Attached::One) }
  end
end
