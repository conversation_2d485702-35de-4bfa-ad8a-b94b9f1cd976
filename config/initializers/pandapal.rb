PandaPal.lti_options = {
  title: 'K5 Students Dashboard',
  lti_spec_version: 'v1p3',
  settings_structure: {
    canvas: {
      type: 'Hash',
      required: true,
      properties: {
        base_url: { type: 'String', required: true },
        api_token: { type: 'String', required: true },
        default_time_zone: { type: 'String' },
      }
    },
    # ------------------------------------------
    # Data Shipper Destinations consumed by `inst_data_shipper` gem for Hosted Data.
    # This is an optional array specifying destinations/url where data will be sent.
    # Each element in the array represents a different service that should receive data.
    # For eg. ['hosted-data://<EMAIL>']
    # ------------------------------------------
    data_shipper_destinations: {
      type: 'Array',
      required: false,
      description: 'An array of destinations to send data to.'
    },
    sftp: {
      type: 'Hash',
      required: false,
      properties: {
        file_path: { type: 'String', required: false },
        host: { type: 'String', required: false },
        user: { type: 'String', required: false },
        key: { type: 'String', required: false }
      }
    },
    sftp_endpoints: {
      type: 'Array',
      required: false,
      description: 'An array of sftp endpoints to import data from.',
      item: {
        type: 'Hash',
        properties: {
          file_path: { type: 'String', required: false },
          host: { type: 'String', required: false },
          user: { type: 'String', required: false },
          password: { type: 'String', required: false }
        }
      }
    },
    instance_names: { type: 'Array', required: false }
  }
}

PandaPal.lti_environments = {
  domain: ENV['PROD_DOMAIN'],
  beta_domain: ENV['BETA_DOMAIN'],
  test_domain: ENV['TEST_DOMAIN']
}

PandaPal.lti_custom_params = {
  custom_canvas_user_id: '$Canvas.user.id',
  custom_canvas_role: '$Canvas.membership.roles',
  custom_canvas_course_id: '$Canvas.course.id',
  custom_canvas_account_id: '$Canvas.account.id',
  custom_canvas_user_locale: '$Message.locale',
  custom_canvas_user_timezone: '$Person.address.timezone',
  custom_canvas_shard_id: '$Canvas.shard.id',
  custom_canvas_observee_ids: '$com.instructure.User.observees',
}

PandaPal.stage_navigation(
  :course_navigation,
  enabled: true,
  text: 'K5 Students Dashboard',
  display_type: 'full_width_in_context'
)

PandaPal.stage_navigation(
  :global_navigation,
  enabled: true,
  text: 'K5 Students Dashboard',
  display_type: 'full_width_in_context'
)

PandaPal.stage_navigation(
  :account_navigation,
  enabled: true,
  text: 'K-5 Dashboard Account Settings',
  display_type: 'full_width_in_context',
  visibility: 'admins'
)
