# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiveEvents::GradeChangeEvent, type: :service do
  let(:course) { create(:course, canvas_id: 12) }
  let(:assignment) { create(:assignment, canvas_id: 34, canvas_context_id: course.canvas_id) }
  let(:user) { create(:user, canvas_id: 56) }

  let(:payload) do
    {
      body: {
        submission_id: 78,
        assignment_id: assignment.canvas_id,
        user_id: user.canvas_id,
        points_possible: { 'numberStr' => '100.0' },
        score: { 'numberStr' => '85.0' }
      }
    }.with_indifferent_access
  end

  let(:service) { described_class.new }

  before do
    allow(Rails.logger).to receive(:warn)
    allow(Rails.logger).to receive(:info)
  end

  describe '#process' do
    context 'when assignment is not found' do
      before { assignment.destroy }

      it 'logs a warning and returns early' do
        expect(Rails.logger).to receive(:warn).with(/assignment not found/)
        expect { service.perform(payload) }.not_to change(Submission, :count)
      end
    end

    context 'when user is not found' do
      before { user.destroy }

      it 'logs a warning and returns early' do
        expect(Rails.logger).to receive(:warn).with(/user not found/)
        expect { service.perform(payload) }.not_to change(Submission, :count)
      end
    end

    context 'when assignment and user exist' do
      let(:submission_double) { instance_double(Submission, sync_from_api: true, canvas_id: 78, score: 85.0, points_possible: 100.0) }

      before do
        allow(Submission).to receive(:where).and_return(double(first_or_initialize: submission_double))
        allow(submission_double).to receive(:points_possible=)
        allow(submission_double).to receive(:score=)
      end

      it 'finds or initializes submission with correct attributes' do
        expect(Submission).to receive(:where).with(
          canvas_id: 78,
          canvas_course_id: assignment.course.canvas_id,
          canvas_assignment_id: assignment.canvas_id,
          canvas_user_id: user.canvas_id
        )

        service.perform(payload)
      end

      it 'sets points_possible and score from payload' do
        expect(submission_double).to receive(:points_possible=).with('100.0')
        expect(submission_double).to receive(:score=).with('85.0')

        service.perform(payload)
      end

      it 'syncs submission from API' do
        expect(submission_double).to receive(:sync_from_api)

        service.perform(payload)
      end

      it 'logs successful sync' do
        expect(Rails.logger).to receive(:info).with(/Submission synced/)

        service.perform(payload)
      end
    end

    context 'with cross-shard IDs' do
      let(:global_submission_id) { 10_000_000_000_078 }
      let(:payload) do
        {
          body: {
            submission_id: global_submission_id,
            assignment_id: assignment.canvas_id,
            user_id: user.canvas_id,
            points_possible: { 'numberStr' => '100.0' },
            score: { 'numberStr' => '85.0' }
          }
        }.with_indifferent_access
      end

      let(:submission_double) { instance_double(Submission, sync_from_api: true, canvas_id: 78, score: 85.0, points_possible: 100.0) }

      before do
        allow(Submission).to receive(:where).and_return(double(first_or_initialize: submission_double))
        allow(submission_double).to receive(:points_possible=)
        allow(submission_double).to receive(:score=)
      end

      it 'converts global ID to local ID' do
        expect(Submission).to receive(:where).with(
          canvas_id: 78, # local ID after conversion
          canvas_course_id: assignment.course.canvas_id,
          canvas_assignment_id: assignment.canvas_id,
          canvas_user_id: user.canvas_id
        )

        service.perform(payload)
      end
    end
  end

  describe '#event_info' do
    it 'returns formatted event info with organization name' do
      expect(service.send(:event_info)).to eq("[Org: #{service.current_organization.name}] [GradeChangeEvent]")
    end
  end
end
