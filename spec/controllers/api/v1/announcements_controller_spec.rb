# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::AnnouncementsController, type: :controller do
  render_views
  let(:organization) { current_organization }
  let(:canvas_user) { create(:user, canvas_id: '123') }
  let(:canvas_sync_client) { instance_double(Bearcat::Client) }
  let(:session) do
    PandaPal::Session.create(panda_pal_organization_id: organization.id,
                             data: {
                               canvas_user_id: canvas_user.canvas_id,
                               organization_key: organization.key
                             })
  end

  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }

  let(:sample_announcements) do
    [
      {
        'id' => 1,
        'subject' => 'System Maintenance',
        'message' => 'Scheduled maintenance tonight',
        'start_at' => '2024-01-01T00:00:00Z',
        'end_at' => '2024-01-02T00:00:00Z',
        'icon' => 'warning'
      },
      {
        'id' => 2,
        'subject' => 'New Feature Release',
        'message' => 'Check out our new features',
        'start_at' => '2024-01-03T00:00:00Z',
        'end_at' => '2024-01-04T00:00:00Z',
        'icon' => 'information'
      }
    ]
  end

  before do
    allow(controller).to receive(:canvas_sync_client).and_return(canvas_sync_client)
    allow(User).to receive(:find_by!).and_return(canvas_user)
  end

  describe 'GET #index' do
    context 'when announcements are fetched successfully' do
      before do
        allow(canvas_sync_client).to receive_message_chain(:get, :all_pages!, :to_a)
          .and_return(sample_announcements)
      end

      it 'returns announcements for the user' do
        get :index, params: default_params.merge(user_id: '123'), format: :json

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(sample_announcements)
      end

      it 'calls Canvas API with correct parameters' do
        expect(canvas_sync_client).to receive(:get)
          .with("/api/v1/accounts/#{organization.canvas_account_id}/account_notifications", { as_user_id: '123' })
          .and_return(double(all_pages!: double(to_a: sample_announcements)))

        get :index, params: default_params.merge(user_id: '123'), format: :json
      end
    end

    context 'when announcements are not found' do
      before do
        allow(canvas_sync_client).to receive(:get).and_raise(StandardError.new('Not found'))
      end

      it 'returns error message' do
        get :index, params: default_params.merge(user_id: '123'), format: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['error_message']).to eq('Not found')
      end
    end
  end

  describe 'DELETE #destroy' do
    context 'when announcement is dismissed successfully' do
      before do
        allow(canvas_sync_client).to receive(:delete).and_return(true)
      end

      it 'dismisses the announcement' do
        expect(canvas_sync_client).to receive(:delete)
          .with("/api/v1/accounts/#{organization.canvas_account_id}/account_notifications/1", { as_user_id: '123' })

        delete :destroy, params: default_params.merge(id: '1', user_id: '123'), format: :json

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when there is an error' do
      before do
        allow(canvas_sync_client).to receive(:delete).and_raise(StandardError.new('API Error'))
      end

      it 'returns error message' do
        delete :destroy, params: default_params.merge(id: '999', user_id: '123'), format: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['error_message']).to eq('API Error')
      end
    end
  end

  describe 'when user is not found' do
    before do
      allow(User).to receive(:find_by!).and_raise(ActiveRecord::RecordNotFound)
    end

    it 'returns not found status for index' do
      get :index, params: default_params.merge(user_id: '999'), format: :json

      expect(response).to have_http_status(:not_found)
      expect(JSON.parse(response.body)['error_message']).to eq('User not found')
    end

    it 'returns not found status for destroy' do
      delete :destroy, params: default_params.merge(id: '1', user_id: '999'), format: :json

      expect(response).to have_http_status(:not_found)
      expect(JSON.parse(response.body)['error_message']).to eq('User not found')
    end
  end
end
