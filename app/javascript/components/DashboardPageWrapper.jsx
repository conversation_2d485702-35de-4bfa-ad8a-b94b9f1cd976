import { Route, MemoryRouter as Router, Routes } from 'react-router-dom'
import { View } from '@instructure/ui-view'

const DashboardPageWrapper = (props) => {
  return (
    <View
      as='div'
      padding='0 medium 0 large'
      className='main-page-container'
      height='100%'
      width='100%'
      {...props}
    >
      <Router>
        <Routes>
          <Route path='/' element={props.children} />
        </Routes>
      </Router>
    </View>
  )
}

export default DashboardPageWrapper
