# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UserConfig, type: :model do
  let(:user) { create(:user) }
  let!(:user_config) { create(:user_config, user:) }

  describe 'validations' do
    it 'is valid with valid attributes' do
      expect(user_config).to be_valid
    end

    it 'validates presence of user' do
      expect(user_config).to validate_presence_of(:canvas_user_id)
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:user).with_foreign_key(:canvas_user_id).with_primary_key(:canvas_id) }
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:home_page).with_values(course_view: 0, agenda_view: 1) }
    it { is_expected.to define_enum_for(:theme).with_values(no_theme: 0, forest: 1, winter: 2, space: 3) }
  end

  describe 'defaults' do
    it 'defaults to true for show_notification_tooltip' do
      new_user_config = build(:user_config)
      expect(new_user_config.show_notification_tooltip).to be true
    end

    it 'defaults to forest for theme' do
      new_user_config = build(:user_config)
      expect(new_user_config.theme).to eq('forest')
    end

    it 'defaults to true for audio_enabled' do
      new_user_config = build(:user_config)
      expect(new_user_config.audio_enabled).to be true
    end
  end

  describe 'theme functionality' do
    it 'can set theme to no_theme' do
      user_config.update!(theme: 'no_theme')
      expect(user_config.reload.theme).to eq('no_theme')
    end

    it 'can set theme to winter' do
      user_config.update!(theme: 'winter')
      expect(user_config.reload.theme).to eq('winter')
    end

    it 'can set theme to space' do
      user_config.update!(theme: 'space')
      expect(user_config.reload.theme).to eq('space')
    end

    it 'can toggle audio_enabled' do
      expect(user_config.audio_enabled).to be true
      user_config.update!(audio_enabled: false)
      expect(user_config.reload.audio_enabled).to be false
    end
  end
end
