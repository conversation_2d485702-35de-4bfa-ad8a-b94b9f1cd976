.ic-Layout-wrapper {
  max-width: 100% !important;
}

.empty-border-box {
  content: "";
  height: 3.5rem;
  width: 0.1rem;
  background: rgb(16, 16, 16);
  display: block;
  margin-left: 5px;
  margin-right: 5px;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Balsamiq Sans', cursive;
  width: 100%;
  overflow-x: hidden;
}

[data-react-class]
  {height: 100% !important;}

.container-with-bg {
  background-color: #DFEBFB !important;
}


.css-1j1gydm-view-panel__content {
  background-color: transparent !important;
}

.full-window {
  height: 100% !important;
  min-height: 100vh;
  position: absolute;
  left: 0;
  width: 100%;
  background-color: lightblue;
}

#theme-selection {
  .selected-icon-border {
    border-width: 2px;
    border-style: solid;
    border-color: #1548E6;
    border-radius: 12px;
    display: inline-flex;
    padding: 0;
    margin: 0;
    line-height: 0;
    overflow: hidden;
  }
}

/* Theme System Styles */
.white-text {
  color: white !important;
  fill: white !important;
}

.theme-space .header-text {
  color: #D1D1D1 !important;
  fill: #D1D1D1 !important;
}

.theme-elements-wrapper img {
  max-width: 700px;
}

/* Dashboard Theme Background Colors */
.dashboard-theme-winter {
  background-color: #73C2EF !important;
}

.dashboard-theme-forest {
  background-color: #d9f1fd !important;
}

.dashboard-theme-space {
  background-color: #0e1d30 !important;
}

.dashboard-theme-no_theme {
  background-color: #F5F5F5 !important;
}

/* Bubble Message Container */
.renderBubbleMessage {
  position: absolute;
  right: 0;
  top: 200px;
  z-index: 10;
}

/* Bubble Message Component */
.bubble-message {
  position: absolute;
  background: #fff;
  border: 1px solid #000;
  border-radius: 6px;
  padding: 12px 15px;
  text-align: left;
  line-height: 1.4;
  max-width: 168px;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  /* Accessibility improvements */
  word-wrap: break-word;
  hyphens: auto;
  /* Animation for smooth appearance */
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* Responsive bubble message */
@media (max-width: 768px) {
  .bubble-message {
    max-width: 140px;
    font-size: 14px;
    padding: 10px 12px;
  }

  .renderBubbleMessage {
    right: 10px;
    top: 150px;
  }
}

/* Bubble arrow styling */
.bubble-message::before,
.bubble-message::after {
  top: 100%;
  left: 20px;
  border: solid transparent;
  content: "";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

/* Inner arrow (white) */
.bubble-message::after {
  border-color: rgba(255, 255, 255, 0);
  border-top-color: #fff;
  border-width: 7px;
  margin-left: -7px;
}

/* Outer arrow (border) */
.bubble-message::before {
  border-color: rgba(0, 0, 0, 0);
  border-top-color: #000;
  border-width: 8px;
  margin-left: -8px;
}

/* Agenda View Styles */
.agenda-tab-content {
  z-index: 120;
  background-color: transparent;
}

/* Agenda Tab Navigation Styling */
.agenda-tab-content [role="tablist"] {
  background-color: transparent;
  border-bottom: none;
  padding: 0;
  margin-bottom: 0;
  display: flex;
  gap: 0;
}

/* Individual Tab Styling */
.agenda-tab-content [role="tab"] {
  background-color: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid #e0e0e0 !important;
  border-bottom: none !important;
  border-radius: 8px 8px 0 0 !important;
  margin: 0 !important;
  padding: 12px 20px !important;
  transition: all 0.3s ease-in-out !important;
  font-weight: 500 !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Active Tab */
.agenda-tab-content [role="tab"][aria-selected="true"] {
  background-color: #ffffff !important;
  color: #333333 !important;
  border-color: #e0e0e0 !important;
  z-index: 2 !important;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Inactive Tab Hover */
.agenda-tab-content [role="tab"]:not([aria-selected="true"]):hover {
  background-color: rgba(255, 255, 255, 0.85) !important;
  color: #333333 !important;
}

/* Tab Panel Content */
.agenda-tab-content [role="tabpanel"] {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 0 8px 8px 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: -1px;
  position: relative;
  z-index: 1;
}



/* Responsive tab styling */
@media (max-width: 768px) {
  .agenda-tab-content [role="tab"] {
    padding: 8px 12px !important;
    font-size: 13px !important;
  }

  .agenda-tab-title {
    font-size: 13px;
  }
}

/* Responsive tab styling */
@media (max-width: 768px) {
  .agenda-tab-content [role="tab"] {
    padding: 6px 12px !important;
    font-size: 13px !important;
  }

  .agenda-tab-title {
    font-size: 13px;
  }

  .agenda-tab-content [role="tablist"] {
    padding: 3px;
  }
}

#agenda_view div[role="tabpanel"] {
  box-shadow: 0px 2px 8px rgba(218, 11, 11, 0.18);
  overflow: hidden;
  border: 1px solid #FFFFFF;
  background-color: #FFFFFF;
  border-radius: 0px 6px 6px 6px;
}

/* Progress Bar Component */
#progress-bar {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

#completion-art {
  position: absolute;
  right: 87px;
  z-index: 5;
  /* Ensure completion art doesn't interfere with interactions */
  pointer-events: none;
}

/* Progress bar gradient styling */
#progress-bar-value span span span {
  background: linear-gradient(to right, #1548E6, #62C7FF 100%);
  border-radius: 4px;
  transition: all 0.3s ease-in-out;
}

/* Responsive progress bar */
@media (max-width: 768px) {
  #completion-art {
    right: 60px;
    /* Scale down on mobile */
    transform: scale(0.8);
  }

  #progress-bar {
    gap: 4px;
  }
}
