# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateSubmissions < ActiveRecord::Migration[5.1]
  def change
    create_table :submissions do |t|
      t.bigint :canvas_id, null: false
      t.bigint :canvas_course_id
      t.bigint :canvas_assignment_id
      t.bigint :canvas_user_id
      t.datetime :submitted_at
      t.datetime :due_at
      t.datetime :graded_at
      t.float :score
      t.float :points_possible
      t.boolean :excused
      t.string :workflow_state

      t.timestamps
    end

    add_index :submissions, :canvas_id, unique: true
    add_index :submissions, :canvas_assignment_id
    add_index :submissions, :canvas_course_id
    add_index :submissions, :canvas_user_id
  end
end
