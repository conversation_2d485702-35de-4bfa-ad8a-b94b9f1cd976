# frozen_string_literal: true

class AccountGradingSchemeColor < ApplicationRecord
  belongs_to :account, foreign_key: :canvas_account_id, optional: true
  enum scheme_color_type: { standard: 0, mastery: 1 }

  scope :by_score, lambda { |score|
    score = [0, score.to_d].max

    all.max_by do |el|
      range = el.range_value.to_d * 100
      score >= range ? range : -range
    end
  }

  def range_limit(value, max_value)
    value = value.to_d * 100
    max_value = max_value.to_d * 100

    max_value == 100 ? "#{value} - #{max_value}" : "#{value} - <#{max_value}"
  end
end
