# frozen_string_literal: true

class CreateContextModuleProgressions < ActiveRecord::Migration[7.0]
  def change
    create_table :context_module_progressions do |t|
      t.bigint :canvas_module_id, null: false
      t.bigint :canvas_module_item_id, null: false
      t.bigint :canvas_user_id, null: false
      t.string :canvas_content_type
      t.bigint :canvas_content_id
      t.string :canvas_content_title
      t.string :canvas_page_url
      t.string :requirement_type
      t.string :requirement_status
      t.datetime :lock_at
      t.datetime :due_at
      t.datetime :todo_date

      t.timestamps
    end
    add_index :context_module_progressions, [:canvas_module_id, :canvas_module_item_id, :canvas_user_id], unique: true, name: 'by_canvas_module_id_module_item_id_canvas_user_id'
  end
end
