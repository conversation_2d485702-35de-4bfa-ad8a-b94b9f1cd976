# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ContextModuleProgression, type: :model do
  subject { build(:context_module_progression) }

  describe 'validations' do
    it { should validate_presence_of(:canvas_module_id) }
    it { should validate_presence_of(:canvas_module_item_id) }
    it { should validate_presence_of(:canvas_user_id) }

    it do
      create(:context_module_progression,
             canvas_module_id: 1,
             canvas_module_item_id: 2,
             canvas_user_id: 3)
      should validate_uniqueness_of(:canvas_module_id).scoped_to(:canvas_module_item_id, :canvas_user_id)
    end
  end

  describe 'associations' do
    it { should belong_to(:context_module).with_primary_key(:canvas_id).with_foreign_key(:canvas_module_id).optional }
    it { should belong_to(:context_module_item).with_primary_key(:canvas_id).with_foreign_key(:canvas_module_item_id).optional }
  end
end
