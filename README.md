# README

K5 Students Dashboard
=====================

Client: Stride

This tool replaces the Dashboard for K5 students with a custom dashboard page. This tools also requires the usage of a JS override.

### Architecture

#### Sharding
This application is aware of and mimics <PERSON><PERSON>' sharding approach. It also leverages the multi-DB features added to PandaPal 5.12. See PandaPal README for instructions on configuring.

This app is also aware of Canvas' sharding approach. The `User` model has methods for querying across shards and tracking if a user belongs to multiple shards (See `RefreshCrossShardUsersJob`).

##### Terminology
- "shard": A string key/identifier (added as a column on `Organization`). Maps to which DB instance this `Organization`'s data should be stored on.
- `PandaPal::Organization.name`: The key used when calling `switch_tenant` and the schema name - both as normal.
- "canvas_shard_id": The shard ID that <PERSON>vas would use, without trailing `0`s
- "Shadow Record": When a User exists in multiple shards, <PERSON>vas creates shadow records in each shard that's not the primary. CanvasSync will pulls these records as normal, but this app will need to recognize when a record is a shadow (its `canvas_id` will be sharded).

## LTI Configuration

This is an LTI 1.3 Tool

### PandaPal Organization
This is a fairly standard tool. It needs Canvas credentials. See `config/panda_pal.rb` for settings schema.

```ruby
PandaPal::Organization.create(
  name: '',
  canvas_account_id: 1,
  key: '',
  secret: '',
  salesforce_id: '',
  settings: {
    canvas: {
      base_url: '', # Canvas Instance URL
      api_token: '', # Canvas API Token
      default_time_zone: 'America/Los_Angeles'
    },
    data_shipper_destinations: [],
    sftp: {
      file_path: ''
      host: ''
      user: ''
      key: ''
    }
  }
)
```

### SFTP Endpoints Configuration to Import ZIP files

To enable data import from remote SFTP servers, define the `sftp_endpoints` configuration as an array of endpoint objects for PandaPal::Organization settings.

```ruby
sftp_endpoints: [
  {
    host: "sftp.example.com",
    user: "username",
    password: "password",
    file_path: "/remote/zip/files",
  }
],
instance_names: ["iowak12", "oregonk12"] # optional
```

Each endpoint should include the following keys:
`host` – SFTP server hostname or IP.
`user` – Username for authentication.
`password` – Password for authentication.
`file_path` – Remote directory path where .zip files are stored.
`instance_names` (optional) – Array of substrings. Only .zip files that include any of these substrings in their filenames will be downloaded.

If instance_names is not provided or is empty, all .zip files in the specified directory will be downloaded.

### SFTP Endpoints Configuration to Import Blackout Dates & Learning Coach Information

To enable CSV imports from remote SFTP servers, define the `sftp` configuration as an Hash object for PandaPal::Organization settings.

Each endpoint should include the following keys:

```ruby
sftp: {
  {
    host: "sftp.example.com",
    user: "username",
    password: "password",
    file_path: "/remote/csv/files"
  }
}
```

## Canvas Reports

Ensure the following reports are enabled in Canvas:

- proservices_provisioning_csv
- proserv_student_submissions_csv
- proserv_context_modules_csv
- proserv_course_completion_csv
- proserv_scores_with_letter_grade_csv
- stride_context_module_progress_csv

## Live Events

To enable Live Events, set up a webhook in Canvas with the URL:
`https://{your-app-url}/canvas_sync/api/v1/live_event?org={panda-pal-organization-id}`

The following events are expected by this LTI:

### Assignment

- assignment_created
- assignment_updated

### Enrollment

- enrollment_created
- enrollment_updated

### Grade

- course_grade_change
- course_grade_change

### Module

- module_created
- module_updated

### Module Item

- module_item_created
- module_item_updated

### Submission

- submission_created
- submission_updated
