import axios from "./axios";

export const getUserDashboardConfig = (userId, params = {}) => {
  return axios.get(`/users/${userId}/user_config`, { params: params });
};

export const createUserDashboardConfig = (userId, params) => {
  return axios.post(`/users/${userId}/user_config`, {
    user_config: params,
  });
};

export const updateUserDashboardConfig = (userId, id, params) => {
  return axios.put(`users/${userId}/user_config/${id}`, {
    user_config: params,
  });
};

export const getK5Students = (id, params = {}) => {
  return axios.get(`/users/${id}/dashboard_students`, { params: params });
};


export const getExternalResources = (params = {}) => {
  return axios.get(`/external_resources`, { params: params });
};

export const getStudentCourses = (id, params = {}) => {
  return axios.get(`/students/${id}/courses`, { params: params });
};

export const getStudentCourseProgress = (studentId, courseId, params = {}) => {
  return axios.get(`/students/${studentId}/courses/${courseId}`, { params: params });
};

export const getStudentDayDueAssignments = (studentId, params = {}) => {
  return axios.get(`/students/${studentId}/due_assignments`, { params: params });
};

export const getStudentDateLimits = (studentId, params = {}) => {
  return axios.get(`/students/${studentId}/course_date_ranges`, { params: params });
};

export const getStudentWeeklyDueAssignments = (studentId, params = {}) => {
  return axios.get(`/students/${studentId}/weekly_due_assignments`, { params: params });
};

export const getStudentCalendarEvents = (studentId, params = {}) => {
  return axios.get(`/students/${studentId}/calendar_events`, { params: params });
};

export const markEventComplete = (studentId, eventId, params = {}) => {
  return axios.put(`/students/${studentId}/events/${eventId}/mark_event_complete`, params);
};

export const getGradeSchemeColors = (accountId, params = {}) => {
  return axios.get(`/accounts/${accountId}/grade_scheme_colors`, { params: params });
};

export const updateGradeSchemeColor = (accountId, id, params) => {
  return axios.put(`/accounts/${accountId}/grade_scheme_colors/${id}`, {
    grading_scheme: params,
  });
};

export const getAnnouncements = (studentId) => {
  return axios.get(`/users/${studentId}/announcements`);
};

export const dismissAnnouncement = (studentId, announcementId) => {
  return axios.delete(`/users/${studentId}/announcements/${announcementId}`);
};

export const getTeacherContacts = (studentId, params = {}) => {
  return axios.get(`/users/${studentId}/teacher_contacts`, { params: params });
};
