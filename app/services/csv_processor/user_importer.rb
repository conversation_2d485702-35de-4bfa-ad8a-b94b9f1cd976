# frozen_string_literal: true

class CsvProcessor::UserImporter < CsvProcessor::Base
  def process_row(row, idx)
    return if row['sourcedId'].blank?

    user = User.joins(:pseudonyms).where(pseudonyms: { sis_id: row['sourcedId'] }).limit(1).take
    user&.update!(grade_level: row['grades'])
  rescue StandardError => e
    rows_with_errors << idx
    Rails.logger.error "Error processing row: #{row.inspect}. Error: #{e.message}. Backtrace: #{e.backtrace}"
  end
end
