# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Submission < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  # include CanvasSync::Concerns::LiveEventSync

  canvas_sync_features :defaults

  validates :canvas_id, uniqueness: true, presence: true
  belongs_to :assignment, primary_key: :canvas_id, foreign_key: :canvas_assignment_id, optional: true
  belongs_to :user, primary_key: :canvas_id, foreign_key: :canvas_user_id, optional: true
  belongs_to :course, primary_key: :canvas_id, foreign_key: :canvas_course_id, optional: true

  api_syncable({
                 canvas_assignment_id: :assignment_id,
                 canvas_user_id: :user_id,
                 submitted_at: :submitted_at,
                 graded_at: :graded_at,
                 cached_due_date: :due_at,
                 score: :score,
                 excused: :excused,
                 workflow_state: :workflow_state
               }, ->(api) { api.user_course_assignment_submission(assignment.context.canvas_id, canvas_assignment_id, canvas_user_id) })

  # HACK: To support Cross Shard updates
  # Returns the canvas_user_id as a string.
  # This method overrides the default getter for canvas_user_id,
  # ensuring that the ID is always returned as a string rather than
  # its native database type (likely an integer).
  def canvas_user_id
    super.to_s
  end

  def requirement_status
    if master?
      'mastered'
    elsif not_master?
      'not mastered'
    elsif not_completed?
      'not completed'
    elsif not_completed_past_due?
      'past due'
    end
  end

  def completed_status
    if graded_at.present? && calculated_score >= 80
      'mastered'
    elsif calculated_score < 80
      'not mastered'
    end
  end

  def master?
    submitted_at.present? && graded_at.present? && calculated_score >= 80
  end

  def not_master?
    submitted_at.present? && graded_at.present? && calculated_score < 80
  end

  def not_completed?
    submitted_at.nil? && !date_passed?
  end

  def not_completed_past_due?
    submitted_at.nil? && date_passed?
  end

  def calculated_score
    return 0.0 if points_possible.to_f <= 0

    (score.to_f / points_possible) * 100
  end

  # HACK: Fallback to assignment points possible if not set on submission
  def points_possible
    self[:points_possible] || assignment.points_possible
  end

  def date_passed?
    due_date = due_at || assignment.due_at
    return false unless due_date

    due_date < Time.current
  end
end
