import { useEffect, useState } from 'react';
import { useParams } from "react-router";

import { ToggleGroup } from '@instructure/ui-toggle-details'
import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { Pill } from '@instructure/ui-pill';
import { IconModuleLine,
         IconWarningBorderlessLine,
         IconXLine,
         IconCheckLine,
         IconStarLine,
         IconStarLightLine } from '@instructure/ui-icons';

import * as API from "../../utils/api";
import CourseModuleItems from './CourseModuleItems';
import { CounterCircle, IconCircle } from '../../shared/components/UtilUI';

const CourseModules = (props) => {
  const { studentId, course } = props;

  const moduleStats = (mod) => {
    const totalItems = Number(mod['items_count']) || 0;
    const masteredItems = Number(mod['mastered']) || 0;
    const pasDueItems = Number(mod['past due']) || 0;
    return (
      <View as="div">
        <Text>
          <Flex>
            <Flex.Item size="30px"><IconModuleLine /></Flex.Item>
            <Flex.Item>{mod['name']}</Flex.Item>
          </Flex>
        </Text>
        <Text size="small">
          <Flex>
            <Flex.Item size="30px"></Flex.Item>
            <Flex.Item>{totalItems} Items</Flex.Item>
            <Flex.Item padding="none x-small"><Text size="large">|</Text></Flex.Item>
            <Flex.Item>
              <CounterCircle
                counts={masteredItems}
                borderColor="success"
                themeOverride={{
                  color: '#0B874B',
                  backgroundPrimary: '#ffeb3b'
                }}
              /> Mastered
            </Flex.Item>
            <Flex.Item padding="none x-small"><Text size="large">|</Text></Flex.Item>
            <Flex.Item>
              <CounterCircle
                counts={pasDueItems}
                borderColor="danger"
                themeOverride={{
                  color: '#E0061F'
                }}
              /> Past Due
            </Flex.Item>
          </Flex>
        </Text>
      </View>
    )
  }

  const renderStarIcons = (item) => {
    let icon = <IconCircle renderIcon={<IconStarLightLine />}
                background="secondary"
               />

    switch (item['requirement_status']) {
      case 'not mastered':
        icon = <IconCircle renderIcon={<IconXLine />}
                background="danger"
               />
        break;
      case 'past due':
        icon = <IconCircle renderIcon={<IconWarningBorderlessLine />}
                borderColor="danger"
                themeOverride={{
                  color: '#E0061F'
                }}/>
        break;
      case 'mastered':
        icon = <IconCircle renderIcon={<IconStarLine />}
                borderColor="success"
                themeOverride={{
                  color: '#0B874B',
                  backgroundPrimary: '#ffeb3b'
                }}/>
        break;
      case 'completed':
        icon = <IconCircle renderIcon={<IconCheckLine />}
                borderColor="success"
                themeOverride={{
                  color: '#0B874B'
                }}/>
        break;
    }

    return (
      <View padding="none xxx-small none none" key={'ico_'+(item['canvas_assignment_id'] || item['id'])}>{icon}</View>
    )
  }

  const moduleStars = (mod) => {
    const items = mod.items;
    if (items) {
      return items.map(item => {
        return (
          renderStarIcons(item)
        )
      })
    }
  }

  const moduleStatus = (mod) => {
    const completedItems = (Number(mod['mastered']) || 0) + (Number(mod['not mastered']) || 0) + (Number(mod['completed']) || 0)
    const totalItems = Number(mod['items_count']) || 0;
    if(totalItems == completedItems){
      return (
        <View>
        <Pill
          renderIcon={<IconCheckLine />}
          color="success"
          themeOverride={{
            textFontWeight: '500'
          }}
        >
          Completed
        </Pill>
        </View>
      )
    } else {
      return (
        <View><Text fontStyle="italic" size="small">In Progress</Text></View>
      )
    }
  }

  const moduleSummary = (mod) => {
    return (
      <Flex>
        <Flex.Item size="50%" padding="x-small" textAlign="start">{moduleStats(mod)}</Flex.Item>
        <Flex.Item size="30%" shouldShrink shouldGrow padding="x-small" textAlign="end">{moduleStars(mod)}</Flex.Item>
        <Flex.Item size="13%" padding="x-small" textAlign="center">{moduleStatus(mod)}</Flex.Item>
      </Flex>
    )
  }

  const renderModuleTile = (mod) => {
    return (
      <ToggleGroup
        toggleLabel="This is the toggle button label for screenreaders"
        summary={ moduleSummary(mod) }
        background="default"
        key={'modtile_'+mod['module_id']}
      >
        <View display="block" padding="small">
          <CourseModuleItems courseModule={mod} course={course} studentId={studentId}/>
        </View>
      </ToggleGroup>
    )
  }

  const renderModules = () => {
    const modules = course.modules;
    if (modules) {
      return modules.map(mod => {
        return (
          <Flex.Item key={'mod_'+mod['module_id']}>
            { renderModuleTile(mod) }
          </Flex.Item>
        )
      })
    }
  }

  return(
    <View as="div">
      <Flex direction="column" gap="medium">
        { course && renderModules() }
      </Flex>
    </View>
  )
}

export default CourseModules
