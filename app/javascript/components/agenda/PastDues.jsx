import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Flex } from '@instructure/ui-flex';
import { Link } from '@instructure/ui-link';
import { IconAssignmentLine,
         IconQuizLine,
         IconArrowEndLine,
         IconWarningBorderlessLine,
         IconDocumentLine,
         IconDiscussionLine,
         IconLtiLine,
         IconFolderLine } from '@instructure/ui-icons';

import InstTable from '../../shared/components/InstTable';
import { IconCircle } from '../../shared/components/UtilUI';
import * as Util from "../../utils/helpers";

const PastDues = (props) => {
  const { studentId, dueAssignments } = props;

  const getItemIcon = (itemType) => {
    switch (itemType) {
      case 'Assignment':
        return <IconAssignmentLine />;
      case 'Quizzes::Quiz':
      case 'Quiz':
        return <IconQuizLine />;
      case 'WikiPage':
      case 'Page':
        return <IconDocumentLine />;
      case 'DiscussionTopic':
      case 'Discussion':
        return <IconDiscussionLine />;
      case 'ExternalTool':
        return <IconLtiLine />;
      case 'File':
        return <IconFolderLine />;
      default:
        return <IconAssignmentLine />;
    }
  };

  const renderTitle = (row, layout) => {
    const itemIcon = getItemIcon(row.item_type);
    // Use course_content_path for all module items
    let item_canvas_url = Util.getItemCanvasUrl(row['course_content_path'], studentId);

    return (
      <View as="div">
        <Flex direction="column">
          <Flex.Item><Text size="x-small">{row['course_name']}</Text></Flex.Item>
          <Flex.Item>
            <Text color="brand">
              <Link
               isWithinText={false}
               href={item_canvas_url}
               onClick={Util.handleCanvasAssignmentUrlNavigation(item_canvas_url, row['canvas_course_id'], row['canvas_assignment_id'] || row['id'], studentId)}
               target="_parent"
               renderIcon={itemIcon}
              >
                {row['assignment_title']}
              </Link>
            </Text>
          </Flex.Item>
        </Flex>
      </View>
    )
  }

  const renderItemLink = (row, layout) => {
    // Use course_content_path for all module items
    let item_canvas_url = Util.getItemCanvasUrl(row['course_content_path'], studentId);

    let returnVal = <Link
                     isWithinText={false}
                     href={item_canvas_url}
                     onClick={Util.handleCanvasAssignmentUrlNavigation(item_canvas_url, row['canvas_course_id'], row['canvas_assignment_id'] || row['id'], studentId)}
                     target="_parent"
                     renderIcon={<IconArrowEndLine />}
                     iconPlacement="end"
                    >
                    Go
                    </Link>

    return (
      <View>{returnVal}</View>
    )
  }

  const renderDueDate = (row, layout) => {
    let icon = <IconCircle renderIcon={<IconWarningBorderlessLine />}
            borderColor="danger"
            height="1.6rem"
            width="1.6rem"
            themeOverride={{
              color: '#E0061F'
            }}/>
    let dueDate = row['submission_due_at'] || row['assignment_due_at'] || row['todo_date'];
    let text = Util.formatDateString(dueDate);
    return (
      <View key={'ico_'+row['id']}><Text size="small">{icon} <strong>Due:</strong> {text}</Text></View>
    )
  }

  const renderItemHeader = () => {
    let text = 'Items'
    if (dueAssignments) {
      text = `Items (${dueAssignments.length})`
    }

    return (
      <Text size="small">{text}</Text>
    )
  }

  const tableHeaders = [
    {
      id: 'items',
      text: renderItemHeader(),
      width: '65%',
      renderCell: renderTitle
    },
    {
      id: 'dueDate',
      text: '',
      renderCell: renderDueDate
    },
    {
      id: 'itemLink',
      text: '',
      textAlign: 'end',
      renderCell: renderItemLink
    }
  ]

  const renderAssignments = () => {
    return (
      <InstTable
        headers={tableHeaders}
        rows={dueAssignments}
      />
    )
  }

  return (
    <View as="div">
      <View as="div" display='block' padding='small' textAlign='start'>
        <Text weight="bold">Past Due Lessons</Text>
      </View>
      <View as="div" display='block' padding='small' textAlign='start'>
        { dueAssignments.length > 0 ? (
            renderAssignments()
          ) : (
            <View as="div" textAlign="start">
              <Text size="medium" color="secondary">No Items Due</Text>
            </View>
          )
        }
      </View>
    </View>
  )
}

export default PastDues
