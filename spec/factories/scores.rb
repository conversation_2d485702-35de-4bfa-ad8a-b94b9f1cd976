# frozen_string_literal: true

FactoryBot.define do
  factory :score do
    canvas_enrollment_id { create(:enrollment).canvas_id }
    sequence(:current_score) { |n| n }
    sequence(:current_letter_grade, ('A'..'F').cycle)
    sequence(:unposted_current_score) { |n| n }
    sequence(:unposted_letter_grade, ('A'..'F').cycle)
    sequence(:final_score) { |n| n }
    sequence(:final_letter_grade, ('A'..'F').cycle)
    workflow_state { 'active' }
  end
end
