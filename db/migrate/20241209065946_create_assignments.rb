# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateAssignments < ActiveRecord::Migration[5.1]
  def change
    create_table :assignments do |t|
      t.bigint :canvas_id, null: false
      t.string :title
      t.text :description
      t.datetime :due_at
      t.datetime :unlock_at
      t.datetime :lock_at
      t.float :points_possible
      t.float :min_score
      t.float :max_score
      t.float :mastery_score
      t.string :grading_type
      t.string :submission_types
      t.string :workflow_state
      t.integer :canvas_context_id
      t.string :canvas_context_type
      t.integer :canvas_assignment_group_id
      t.integer :canvas_grading_scheme_id
      t.integer :canvas_grading_standard_id

      t.timestamps
    end

    add_index :assignments, :canvas_id, unique: true
    add_index :assignments, [:canvas_context_id, :canvas_context_type]
  end
end
