import { createContext, useContext, useState, useEffect } from 'react';
import {
  getCourseThemeBackground,
  getAnnouncementThemeBackground,
  getResourceThemeBackground,
  getThemeElements,
  getThemeColors
} from '@/utils/themeHelper';

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState('no_theme');
  const [themeAssets, setThemeAssets] = useState({
    background: {},
    elements: {},
    colors: {}
  });

  const buildThemeAssets = (theme) => {
    return {
      background: {
        course: getCourseThemeBackground(theme),
        announcement: getAnnouncementThemeBackground(theme),
        resource: getResourceThemeBackground(theme),
        teacher: getAnnouncementThemeBackground(theme),
      },
      elements: getThemeElements(theme),
      colors: getThemeColors(theme),
    };
  };

  useEffect(() => {
    const theme = window.ENV.user_config.theme;
    setCurrentTheme(theme);
    setThemeAssets(buildThemeAssets(theme));
  }, []);

  const updateTheme = (newTheme) => {
    setCurrentTheme(newTheme);
    setThemeAssets(buildThemeAssets(newTheme));
    window.ENV.user_config.theme = newTheme;
    // Remove previous theme class
    document.body.classList.remove(`dashboard-theme-${currentTheme}`);

    // Add new theme class
    document.body.classList.add(`dashboard-theme-${newTheme}`);
  };

  const value = {
    currentTheme,
    themeAssets,
    setCurrentTheme,
    setThemeAssets,
    updateTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);
