# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Account < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  # include CanvasSync::Concerns::Account::Ancestry # Add support for the ancestry Gem
  # include CanvasSync::Concerns::LiveEventSync

  canvas_sync_features :defaults

  validates :canvas_id, uniqueness: true, presence: true

  has_many :admins, primary_key: :canvas_id, foreign_key: :canvas_account_id
  belongs_to :canvas_parent, class_name: 'Account', optional: true,
                             primary_key: :canvas_id, foreign_key: :canvas_parent_account_id
  has_many :sub_accounts, class_name: 'Account',
                          primary_key: :canvas_id, foreign_key: :canvas_parent_account_id
  has_many :groups, primary_key: :canvas_id, foreign_key: :canvas_account_id
  has_many :account_grading_scheme_colors, primary_key: :canvas_id, foreign_key: :canvas_account_id

  scope :active, -> { where.not(workflow_state: 'deleted') }
  # scope :should_canvas_sync, -> { active } # Optional - uses .active if not given

  api_syncable({
                 name: :name,
                 workflow_state: :workflow_state,
                 canvas_parent_account_id: :parent_account_id
               }, ->(api) { api.account(canvas_id) })
end
