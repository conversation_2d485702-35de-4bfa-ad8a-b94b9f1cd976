# frozen_string_literal: true

def canvas_sync_client
  Bearcat::Client.new(
    prefix: current_organization.settings[:canvas][:base_url],
    token: current_organization.settings[:canvas][:api_token],
    master_rate_limit: Rails.env.production?,
  )
end

def default_standard_grading_scheme
  [
    { 'name' => 'A', 'value' => 0.94, 'default_color_code' => '#027A48' },
    { 'name' => 'A-', 'value' => 0.90, 'default_color_code' => '#027A48' },
    { 'name' => 'B+', 'value' => 0.87, 'default_color_code' => '#387C0F' },
    { 'name' => 'B', 'value' => 0.84, 'default_color_code' => '#387C0F' },
    { 'name' => 'B-', 'value' => 0.80, 'default_color_code' => '#387C0F' },
    { 'name' => 'C+', 'value' => 0.77, 'default_color_code' => '#B54708' },
    { 'name' => 'C', 'value' => 0.74, 'default_color_code' => '#B54708' },
    { 'name' => 'C-', 'value' => 0.70, 'default_color_code' => '#B54708' },
    { 'name' => 'D+', 'value' => 0.67, 'default_color_code' => '#B93815' },
    { 'name' => 'D', 'value' => 0.64, 'default_color_code' => '#B93815' },
    { 'name' => 'D-', 'value' => 0.61, 'default_color_code' => '#B93815' },
    { 'name' => 'F', 'value' => 0.0, 'default_color_code' => '#B42318' }
  ]
end

def default_mastery_grading_scheme
  [
    { 'name' => 'Pass', 'value' => 0.61, 'default_color_code' => '#027A48' },
    { 'name' => 'Fail', 'value' => 0.0, 'default_color_code' => '#B42318' }
  ]
end

def parse_bool(bool)
  ActiveModel::Type::Boolean.new.cast(bool)
end
