# frozen_string_literal: true

class Api::V1::StudentsController < ApplicationController
  skip_authorization_check
  before_action :set_user, except: [:mark_event_complete]

  # show all student courses list
  def courses
    @records = QueryBuilder::StudentCourses.across_shard(@user)
    @default_course_image = GlobalSetting.where(setting_type: 'default_course_image').last
  end

  def course_progress
    @course_progress = QueryBuilder::StudentCourseProgress.from_valid_shard(@user, { canvas_course_id: params[:course_id], course_shard_id: params[:org_shard_id], filters: params[:filters] })

    render json: @course_progress
  rescue StandardError => e
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  def course_date_ranges
    @record = @user.course_date_ranges[0]
  rescue StandardError => e
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  def due_assignments
    # Fetch all module requirements (assignments and non-assignments) from all shards for the student
    @assignments = QueryBuilder::StudentDueAssignments.across_shard(@user)

    @assignments = filter_past_due_assignments(@assignments) if params[:only_past_dues].present?
    @assignments = filter_assignments_by_date(@assignments) if params[:due_date].present?
    @assignments = sort_assignments_by_due_date(@assignments)
  end

  def weekly_due_assignments
    # Fetch all module requirements (assignments and non-assignments) from all shards for the student
    @assignments = QueryBuilder::StudentDueAssignments.across_shard(@user)

    # Set week date range (Monday to Friday)
    date = Time.zone.parse(params[:due_date])
    @start_date = date.beginning_of_week
    @end_date = @start_date + 4.days

    # Filter items by week range - using select instead of where because items come from multiple shards
    @assignments = @assignments.select do |item|
      due_at = item['submission_due_at'] || item['assignment_due_at'] || item['todo_date']
      due_at.present? && due_at.between?(@start_date, @end_date.end_of_day)
    end

    # Group items by due date (YYYY-MM-DD format)
    @records = @assignments.each_with_object({}) do |item, grouped_records|
      due_at = item['submission_due_at'] || item['assignment_due_at'] || item['todo_date']
      date_key = Time.zone.parse(due_at.to_s).strftime('%Y-%m-%d')
      grouped_records[date_key] ||= []
      grouped_records[date_key] << item
    end
  end

  def calendar_events
    date_range = build_date_range(params[:due_date], params[:weekly])
    all_events = collect_events_from_shards(date_range)
    all_events.uniq! { |e| e[:sharded_canvas_id] }
    @records = all_events.group_by { |event| event[:start_at].strftime('%Y-%m-%d') }
  end

  def mark_event_complete
    with_organization(params[:organization_name]) do
      user = User.find_by!(canvas_id: params[:id])
      event = CalendarEvent.new(canvas_id: params[:event_id])

      CompletedCalendarEvent.find_or_create_by!(canvas_user_id: user.canvas_id, canvas_id: event.canvas_id)

      # Define a reusable lambda function to find and mark events as complete
      # This lambda takes a relation (e.g., CalendarEvent) and a canvas_id
      # It filters events that:
      #   1. Have a start_at date up to the end of the current day
      #   2. Match the provided canvas_id
      # Then marks all matching events as completed by updating the completed_at timestamp

      # If the event belongs to a different organization than the current one,
      # we need to switch to that organization's tenant to update the event there
      unless event.home_organization.name == current_organization.name
        event.home_organization.switch_tenant do
          CompletedCalendarEvent.find_or_create_by!(canvas_user_id: user.primary_record.canvas_id, canvas_id: event.primary_record.canvas_id)
        end
      end

      # Calendar events are duplicated across shards for cross-shard accessibility
      # We need to update the completion status on all shards for consistency
      # This iterates through all the user's shards and updates the event on each one
      user.against_shards do |shard_user|
        CompletedCalendarEvent.find_or_create_by!(canvas_user_id: shard_user.canvas_id, canvas_id: event.sharded_canvas_id)
      end
    end

    head :ok
  end

  private

  def set_user
    @user = User.find_by(canvas_id: params[:id])
  end

  def build_date_range(due_date, weekly)
    date = Time.zone.parse(due_date)

    if weekly
      start_date = date.beginning_of_week
      start_date.beginning_of_day..(start_date + 4.days).end_of_day
    else
      date.all_day
    end
  end

  def collect_events_from_shards(date_range)
    all_events = []

    @user.against_shards do |shard_user, org|
      events = shard_user.canvas_calendar_events(date_range)
      completed_events = fetch_completed_events(shard_user, events)
      all_events += build_event_data(events, completed_events, shard_user, org)
    end

    all_events
  end

  def fetch_completed_events(shard_user, events)
    CompletedCalendarEvent
      .where(canvas_user_id: shard_user.canvas_id, canvas_id: events.map(&:canvas_id))
      .index_by(&:canvas_id)
  end

  def build_event_data(events, completed_events, shard_user, org)
    events.map do |event|
      completed = completed_events[event.canvas_id]

      {
        canvas_id: event.canvas_id.to_s,
        title: event.title,
        start_at: event.start_at,
        end_at: event.end_at,
        completed_at: completed&.created_at,
        html_url: event.html_url,
        html_path: event.html_path(org),
        canvas_user_id: shard_user.canvas_id,
        sharded_canvas_id: event.sharded_canvas_id.to_s,
        organization_name: org.name
      }
    end
  end

  def filter_past_due_assignments(assignments)
    assignments.select do |item|
      due_at = extract_due_date(item)
      req_status = item['req_status']

      # Include items that are past due and not completed
      due_at.present? && due_at < Date.current.beginning_of_day &&
        !%w[mastered completed].include?(req_status)
    end
  end

  def filter_assignments_by_date(assignments)
    target_date = Time.zone.parse(params[:due_date])
    assignments.select do |item|
      due_at = extract_due_date(item)
      due_at.present? && due_at.between?(target_date.beginning_of_day, target_date.end_of_day)
    end
  end

  def sort_assignments_by_due_date(assignments)
    assignments.sort_by do |item|
      extract_due_date(item) || Time.current
    end
  end

  def extract_due_date(item)
    item['submission_due_at'] || item['assignment_due_at'] || item['todo_date']
  end
end
