import React from "react";
import { Select } from '@instructure/ui-select';
import { Alert } from '@instructure/ui-alerts';

class InstSelect extends React.Component {
  state = {
    inputValue: this.getOptionById(this.props.inputValue).label,
    isShowingOptions: false,
    highlightedOptionId: null,
    selectedOptionId: this.props.selectedOptionId,
    announcement: null
  }

  getOptionById(queryId) {
    return queryId ? this.props.options.find(({ id }) => id === queryId) : ''
  }

  handleShowOptions = (event) => {
    this.setState({
      isShowingOptions: true
    })
  }

  handleHideOptions = (event) => {
    const { selectedOptionId } = this.state
    const option = this.getOptionById(selectedOptionId).label
    this.setState({
      isShowingOptions: false,
      highlightedOptionId: null,
      inputValue: selectedOptionId ? option : '',
      announcement: 'List collapsed.'
    })
  }

  handleBlur = (event) => {
    this.setState({
      highlightedOptionId: null
    })
  }

  handleHighlightOption = (event, { id }) => {
    event.persist()
    const optionsAvailable = `${this.props.options.length} options available.`
    const nowOpen = !this.state.isShowingOptions
      ? `List expanded. ${optionsAvailable}`
      : ''
    const option = this.getOptionById(id).label
    this.setState((state) => ({
      highlightedOptionId: id,
      inputValue: event.type === 'keydown' ? option : state.inputValue,
      announcement: `${option} ${nowOpen}`
    }))
  }

  handleSelectOption = (event, { id }) => {
    const option = this.getOptionById(id).label
    this.setState({
      selectedOptionId: id,
      inputValue: option,
      isShowingOptions: false,
      announcement: `"${option}" selected. List collapsed.`
    })
    this.props.onChange(id, option.label);
  }

  render() {
    const {
      inputValue,
      isShowingOptions,
      highlightedOptionId,
      selectedOptionId,
      announcement
    } = this.state

    return (
      <div>
        <Select
          renderLabel={this.props.label}
          placeholder={this.props.placeholder || "Select"}
          assistiveText="Use arrow keys to navigate options."
          inputValue={inputValue}
          isShowingOptions={isShowingOptions}
          onBlur={this.handleBlur}
          onRequestShowOptions={this.handleShowOptions}
          onRequestHideOptions={this.handleHideOptions}
          onRequestHighlightOption={this.handleHighlightOption}
          onRequestSelectOption={this.handleSelectOption}
        >
          {this.props.options.map((option) => {
            return (
              <Select.Option
                id={option.id}
                key={option.id}
                isHighlighted={option.id === highlightedOptionId}
                isSelected={option.id === selectedOptionId}
              >
                {option.label}
              </Select.Option>
            )
          })}
        </Select>
      </div>
    )
  }
}

export default InstSelect

// render(
//   <View>
//     <SingleSelectExample
//       options={[
//         { id: 'opt1', label: 'Alaska' },
//         { id: 'opt2', label: 'American Samoa' },
//         { id: 'opt3', label: 'Arizona' },
//         { id: 'opt4', label: 'Arkansas' },
//         { id: 'opt5', label: 'California' },
//         { id: 'opt6', label: 'Colorado' },
//         { id: 'opt7', label: 'Connecticut' },
//         { id: 'opt8', label: 'Delaware' },
//         { id: 'opt9', label: 'District Of Columbia' },
//         { id: 'opt10', label: 'Federated States Of Micronesia' },
//         { id: 'opt11', label: 'Florida' },
//         { id: 'opt12', label: 'Georgia (unavailable)' },
//         { id: 'opt13', label: 'Guam' },
//         { id: 'opt14', label: 'Hawaii' },
//         { id: 'opt15', label: 'Idaho' },
//         { id: 'opt16', label: 'Illinois' }
//       ]}
//     />
//   </View>
// )