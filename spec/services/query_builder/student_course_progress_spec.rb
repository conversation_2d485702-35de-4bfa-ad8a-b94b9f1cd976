# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueryBuilder::StudentCourseProgress do
  describe '#filtered_out?' do
    let(:query_builder) { described_class.new({}) }

    # Create submission doubles for each status type
    let(:mastered_submission) do
      instance_double(Submission,
                      master?: true,
                      not_master?: false,
                      not_completed?: false,
                      not_completed_past_due?: false,
                      requirement_status: 'mastered')
    end

    let(:not_mastered_submission) do
      instance_double(Submission,
                      master?: false,
                      not_master?: true,
                      not_completed?: false,
                      not_completed_past_due?: false,
                      requirement_status: 'not mastered')
    end

    let(:not_completed_submission) do
      instance_double(Submission,
                      master?: false,
                      not_master?: false,
                      not_completed?: true,
                      not_completed_past_due?: false,
                      requirement_status: 'not completed')
    end

    let(:past_due_submission) do
      instance_double(Submission,
                      master?: false,
                      not_master?: false,
                      not_completed?: false,
                      not_completed_past_due?: true,
                      requirement_status: 'past due')
    end

    context 'when filters is blank' do
      before do
        allow(query_builder).to receive(:filters).and_return(nil)
      end

      it 'returns false for any submission' do
        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end
    end

    context 'when filters is an empty array' do
      before do
        allow(query_builder).to receive(:filters).and_return([])
      end

      it 'returns false for any submission' do
        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end
    end

    context 'with single filter' do
      it 'filters out mastered submissions when master filter is active' do
        allow(query_builder).to receive(:filters).and_return(['master'])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out not_mastered submissions when not_master filter is active' do
        allow(query_builder).to receive(:filters).and_return(['not_master'])

        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out not_completed submissions when not_completed filter is active' do
        allow(query_builder).to receive(:filters).and_return(['not_completed'])

        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be true
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out past_due submissions when not_completed_past_due filter is active' do
        allow(query_builder).to receive(:filters).and_return(['not_completed_past_due'])

        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be true
      end
    end

    context 'with multiple filters' do
      it 'filters out submissions matching any of the filters' do
        allow(query_builder).to receive(:filters).and_return(%w[master not_completed])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be true
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out mastered and not_mastered submissions when both filters are active' do
        allow(query_builder).to receive(:filters).and_return(%w[master not_master])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out not_completed and past_due submissions when both filters are active' do
        allow(query_builder).to receive(:filters).and_return(%w[not_completed not_completed_past_due])

        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be true
        expect(query_builder.filtered_out?(past_due_submission)).to be true
      end

      it 'filters out all status types when all filters are active' do
        allow(query_builder).to receive(:filters).and_return(%w[master not_master not_completed not_completed_past_due])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_completed_submission)).to be true
        expect(query_builder.filtered_out?(past_due_submission)).to be true
      end
    end

    context 'with complex submission statuses' do
      # Edge case: submission that doesn't cleanly fit into one category
      let(:ambiguous_submission) do
        instance_double(Submission,
                        master?: false,
                        not_master?: false,
                        not_completed?: false,
                        not_completed_past_due?: false,
                        requirement_status: 'unknown')
      end

      it 'does not filter out submissions that do not match any known status' do
        allow(query_builder).to receive(:filters).and_return(%w[master not_master not_completed not_completed_past_due])

        expect(query_builder.filtered_out?(ambiguous_submission)).to be false
      end
    end

    context 'with filters containing unknown values' do
      it 'ignores unknown filter values' do
        allow(query_builder).to receive(:filters).and_return(%w[unknown_filter master])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end
    end
  end
end
