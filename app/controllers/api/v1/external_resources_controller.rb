# frozen_string_literal: true

class Api::V1::ExternalResourcesController < ApplicationController
  skip_before_action :verify_authenticity_token
  skip_before_action :forbid_access_if_lacking_session
  skip_authorization_check

  include Rails.application.routes.url_helpers

  def index
    @external_resources = ExternalResource.all.order(:position)
  end

  def create
    @external_resource = ExternalResource.new(upload_params)
    @external_resource.banner.attach(upload_params[:banner])
    @external_resource.save!

    render json: json_response, status: :ok
  rescue StandardError => e
    render json: {
      message: e.message
    }, status: :unprocessable_entity
  end

  def update
    @external_resource = ExternalResource.find(params[:id])
    @external_resource.assign_attributes(upload_params)
    @external_resource.save! if @external_resource.changed?

    render json: json_response, status: :ok
  rescue StandardError => e
    render json: {
      message: e.message
    }, status: :unprocessable_entity
  end

  def destroy
    @external_resource = ExternalResource.find(params[:id])
    @external_resource.destroy
  end

  private

  def upload_params
    params.require(:upload).permit(:banner, :base_url, :position, :description)
  end

  def json_response
    {
      id: @external_resource.id,
      base_url: @external_resource.base_url,
      position: @external_resource.position,
      description: @external_resource.description,
      file_name: @external_resource.banner&.filename,
      file_url: @external_resource.banner.present? ? rails_blob_url(@external_resource.banner, only_path: true) : ''
    }
  end
end
