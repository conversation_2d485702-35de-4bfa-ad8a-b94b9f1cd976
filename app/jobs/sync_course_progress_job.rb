# frozen_string_literal: true

class SyncCourseProgressJob < CanvasSync::Jobs::ReportStarter
  MAPPING = {
    conflict_target: [:canvas_user_id, :canvas_course_id],
    report_columns: {
      canvas_user_id: {
        report_column: :student_id,
        type: :integer
      },
      canvas_course_id: {
        report_column: :course_id,
        type: :integer
      },
      requirement_count: {
        report_column: :requirement_count,
        type: :integer
      },
      requirement_completed_count: {
        report_column: :requirement_completed_count,
        type: :integer
      },
      completion_date: {
        report_column: :completion_date,
        type: :datetime
      }
    }
  }.with_indifferent_access.freeze

  def perform(options)
    super(
      'proserv_course_completion_csv',
      merge_report_params(options, {
                            course_ids: Course.selectable.pluck(:canvas_id).join(',')
                          }, term_scope: false),
      CourseProgressReportProcessor.to_s,
      {},
    )
  end

  class CourseProgressReportProcessor
    def self.process(report_file_path, _options, _report_id)
      CanvasSync::Importers::BulkImporter.import(
        report_file_path,
        MAPPING[:report_columns],
        CourseProgress,
        MAPPING[:conflict_target]
      )
    end
  end
end
