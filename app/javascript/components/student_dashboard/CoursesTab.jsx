import { useEffect, useState} from 'react';
import { useNavigate } from "react-router";

import { View } from '@instructure/ui-view';
import { ProgressBar } from '@instructure/ui-progress';
import { Text } from '@instructure/ui-text';
import { Img } from '@instructure/ui-img';
import { MetricGroup, Metric } from '@instructure/ui-metric';
import { Flex } from '@instructure/ui-flex';
import { Link } from '@instructure/ui-link';
import { Alert } from '@instructure/ui-alerts';
import { InfoAlert } from '../../shared/components/UtilUI';
import { Spinner } from '@instructure/ui-spinner';

import * as API from "../../utils/api";

const CoursesTab = (props) => {
  const navigate = useNavigate();
  const { studentId } = props;

  const [courses, setCourses] = useState([]);
  const [isCoursesLoading, setIsCoursesLoading] = useState(true);
  const [defaultImageUrl, setDefaultImageUrl] = useState(null);

  useEffect(() => {
    setIsCoursesLoading(true);
    getCourses();
  }, [studentId]);

  const getCourses = () => {
    API.getStudentCourses(studentId)
      .then((response) => response.data)
      .then((response) => {
        setCourses(response.courses);
        setDefaultImageUrl(response.default_image_url);
        setIsCoursesLoading(false);
        redirectToPreSelectedCourseIfPresent();
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const courseImageTile = (course) => {
    let imageUrl = null;
    imageUrl = (course.course_image_url) ? course.course_image_url : defaultImageUrl;

    if (imageUrl) {
      return <Img src={imageUrl} alt={course.course_code} constrain="cover"/>
    }
  }

  const courseProgressPercent = (course) => {
    return Math.round(course.requirement_completed_count / course.requirement_count * 100)
  }

  const renderScore = (course) => {
    const current_score = course.current_score ? course.current_score + '%' : '- -';
    const grade = course.current_letter_grade ? course.current_letter_grade : '- -';
    return (
      <Flex justifyItems="end">
        <Flex.Item>
          <Metric renderLabel="Current Score" renderValue={current_score} />
        </Flex.Item>
        <Flex.Item>
          <View as="div" className="empty-border-box"></View>
        </Flex.Item>
        <Flex.Item>
          <Metric renderLabel="Grade" renderValue={grade} />
        </Flex.Item>
      </Flex>
    )
  }

  const navigateToCourse = (courseId, orgShardId) => {
    return navigate(`${studentId}/course/${courseId}?orgShardId=${orgShardId}`)
  }

  const randomCourseColor = (index) => {
    const colors = ["#0374B5", "#e317dd", "#ddc942", "#42ddcb", "#8f42dd", "#c3dd42", "#394B58"];
    return colors[index%4]
  }

  const renderCard = (course, index) => {
    const cardColor = randomCourseColor(index);
    return (
      <Link onClick={() =>{ navigateToCourse(course.canvas_course_id, course.organization_shard_id) }} key={'lk_'+course.id}>
        <View
          as="div"
          display="inline-block"
          width="17rem"
          height="24rem"
          margin="medium"
          textAlign="center"
          shadow="above"
          background="primary"
          borderRadius="medium"
          key={'v1_'+course.id}
        >
          <View as="div"
            height="9rem"
            background="info"
            borderRadius="medium"
            textAlign="center"
            overflowX="auto"
            overflowY="auto"
            themeOverride={{
              backgroundInfo: `${cardColor}`
            }}
            key={'l1v1_'+course.id}
          >
            { courseImageTile(course) }
          </View>
          <View as="div"
            height="8.5rem"
            textAlign="start"
            themeOverride={{
              fontFamily: '__Balsamiq Sans_5'
            }}
            key={'l1v2_'+course.id}
          >
            <View as="div" padding="small" height="4rem" key={'l2v1_'+course.id}>
              <Text color="brand" wrap="break-word">{course.name}</Text>
            </View>
            <View
              as="div"
              key={'l2v2_'+course.id}
              >
              { renderScore(course) }
            </View>
          </View>
          <hr/>
          <View as="div" height="5rem" key={'v2_'+course.id}>
            <View as="div" textAlign="start" padding="x-small" key={'v2.1_'+course.id}><Text transform="capitalize">Completion</Text></View>
            <View as="div" padding="x-small" key={'v2.2_'+course.id}>
              <ProgressBar
                valueNow={course.requirement_completed_count}
                valueMax={course.requirement_count}
                screenReaderLabel='Course Progress'
                renderValue={() => { return `${courseProgressPercent(course)} %` }}
                margin="0 0 small"
                themeOverride={{
                  trackColor: '#2d3b4514'
                }}
                key={'v2prog_'+course.id}
              />
            </View>
          </View>
        </View>
      </Link>
    )
  }

  // Navigate to preselected course when coming back from Course [Launch K-5 Dashboard] button
  const redirectToPreSelectedCourseIfPresent = () => {
    // Ensure if valid assignment_course_id is set, navigate to that course
    if (window.ENV.assignment_course_id && window.ENV.assignment_course_id != '$Canvas.course.id') {
      const courseId = window.ENV.assignment_course_id;
      const shardId = window.ENV.canvas_shard_id;

      // Clear the reference before navigation
      window.ENV.assignment_course_id = null;

      // Navigate using the local copies of the values
      navigateToCourse(courseId, shardId);
    }
  }

  const renderNoCourses = () => {
    return (
      <View as="div" textAlign="start" margin="medium none" width="50%">
        <InfoAlert
          title="No Courses to Display:"
          subText="Customer provided no courses text will be displayed here. Lorem ipsum dolor sit amet, felis eu sem consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Pretium quis massa. Sociis natoque penatibus nulla."
        />
      </View>
    )
  }

  const renderCourses = () => {
    if (courses.length === 0) {
      return renderNoCourses();
    } else {
      return courses.map( (course, index) => {
        return renderCard(course, index)
      })
    }
  }

  // Show loader while courses are loading
  if (isCoursesLoading) {
    return (
      <Flex justifyItems="center" alignItems="center" height="100%">
        <Spinner renderTitle="Loading courses..." size="medium" />
      </Flex>
    );
  }

  return (
    <View as="div">
      <View as="div" textAlign="start" margin="small medium" className='header-text'>
        <Text size="large" weight="bold" >My Courses ({courses.length})</Text>
      </View>
      <View as="div" textAlign="start">
        { renderCourses() }
      </View>
    </View>
  )
}

export default CoursesTab
