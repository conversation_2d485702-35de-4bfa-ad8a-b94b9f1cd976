import { useEffect, useLayoutEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { Grid } from '@instructure/ui-grid';
import { Link } from '@instructure/ui-link';
import { Pill } from '@instructure/ui-pill';
import { Button } from '@instructure/ui-buttons';
import { IconAssignmentLine,
         IconQuizLine,
         IconCalendarMonthLine,
         IconCheckMarkSolid,
         IconDocumentLine,
         IconDiscussionLine,
         IconLtiLine,
         IconFolderLine } from '@instructure/ui-icons';

import { DateTime } from 'luxon';
import * as API from "../../utils/api";
import * as GlobalConstant from '../../utils/constants';
import * as Util from "../../utils/helpers";

const WeekItemGrid = (props) => {
  const { studentId, records, calendarEvents, startDate, endDate, setFlag } = props;

  const [combinedItems, setCombinedItems] = useState([]);

  const getItemIcon = (itemType) => {
    switch (itemType) {
      case 'Assignment':
        return <IconAssignmentLine />;
      case 'Quizzes::Quiz':
      case 'Quiz':
        return <IconQuizLine />;
      case 'WikiPage':
      case 'Page':
        return <IconDocumentLine />;
      case 'DiscussionTopic':
      case 'Discussion':
        return <IconDiscussionLine />;
      case 'ExternalTool':
        return <IconLtiLine />;
      case 'File':
        return <IconFolderLine />;
      case 'calendar_event':
        return <IconCalendarMonthLine />;
      default:
        return <IconAssignmentLine />;
    }
  };

  const parsedDate = (dateStr) => {
    return Util.setDateOnZeroHour(dateStr);
  }

  const modifyDay = (dateStr, step) => {
    let d = parsedDate(dateStr);
    d.setUTCDate(d.getUTCDate() + step);
    return d
  }

  const formatDate = (date) => {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
          month = '0' + month;
      if (day.length < 2)
          day = '0' + day;

      return [year, month, day].join('-');
  }

  const markEventComplete = async (canvasUserId, eventId, organizationName) => {
    await API.markEventComplete(canvasUserId, eventId, { organization_name: organizationName })
      .then(() => {
        setFlag(true)
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const renderDayHeader = (dateStr) => {
    let date = parsedDate(dateStr)
    let day = GlobalConstant.DAY_NAMES[date.getDay()];
    let monthName = GlobalConstant.MONTH_NAMES[date.getMonth()];
    return (
      <View>
        <View as="div">{day}</View>
        <View as="div"><Text weight='bold'>{monthName} {date.getDate()}</Text></View>
      </View>
    )
  }

  const renderGridHeader = (date, step) => {
    let dateStr = modifyDay(date, step)
    return (
      <Grid.Col>{renderDayHeader(dateStr)}</Grid.Col>
    )
  }

  const renderBlackoutDayRowItem = (dateStr) => {
    return (
      Util.isBlackoutDate(dateStr) && <View as="div" borderWidth="small">
        <Flex direction="column">
          <Flex.Item>
            <View as="div" background="secondary"><Text size="medium">Blackout Day</Text></View>
          </Flex.Item>
        </Flex>
      </View>
    )
  }

  useEffect(() => {
    const dateItems = document.querySelectorAll('.date-items');
    dateItems.forEach(item => {
      item.style.minHeight = `25rem`;
    });
  }, [startDate])

  // Set Dynamic Height
  useLayoutEffect(() => {
    const defaultMinHeight = 25 // 25rem
    const dateItems = document.querySelectorAll('.date-items');
    const scrollHeightInRem = document.querySelector('#items-grid').scrollHeight / parseFloat(getComputedStyle(document.documentElement).fontSize);
    const height = scrollHeightInRem > defaultMinHeight ? scrollHeightInRem : defaultMinHeight;

    dateItems.forEach(item => {
      item.style.minHeight = `${height}rem`;
    });
  }, [combinedItems]);

  const renderDayDueItem = (date, step) => {
    let dateStr = modifyDay(date, step);

    // Find the corresponding combined items for the date. We are getting array of items for the current step/day/date
    const itemsForDay = combinedItems[step] || [];

    return (
      <View as="div" borderWidth="small" minHeight="25rem" className='date-items'>
        {renderBlackoutDayRowItem(dateStr)}
        {itemsForDay && itemsForDay.length > 0 ? (
          itemsForDay.map((row) => (
            <View as="div" borderWidth="small" key={row.id}>
              {renderTitle(row)}
            </View>
          ))
        ) : (
          <Flex direction="column">
            <Flex.Item>
              <Text size="medium" textAlign="center" color="secondary">
                No Items Due
              </Text>
            </Flex.Item>
          </Flex>
        )}
      </View>
    )
  }

  const renderTitle = (row) => {
    const isEvent = row.item_type === 'calendar_event';
    const itemIcon = getItemIcon(row.item_type);

    // Use course_content_path for all module items
    let item_canvas_url = Util.getItemCanvasUrl(row['course_content_path'], studentId);

    const linkUrl = isEvent
      ? Util.isSameUser(window.ENV.user.canvas_id, row.canvas_user_id)
        ? Util.canvasCalendarEventUrl(row.html_path)
        : ''
      : item_canvas_url;

    return (
      <View as="div" textAlign="start">
        <Flex direction="column">
          <Flex.Item padding="none x-small">
            <Text size="x-small">
              <b>{isEvent ? 'Event:' : 'Course:'}</b>&nbsp;
              {isEvent
                ? DateTime.fromISO(row['start_at'], { setZone: true })
                          .toFormat('h:mm a') // Force 12-hour + am/pm
                          .replace(/(am|pm)/i, (match) => match.toUpperCase())
                : row['course_name']}
            </Text>
          </Flex.Item>
          <Flex.Item padding="none x-small">
            <Text color="brand">
              <Link
                isWithinText={false}
                href={linkUrl}
                onClick={
                  row.item_type !== 'calendar_event'
                    ? Util.handleCanvasAssignmentUrlNavigation(item_canvas_url, row['canvas_course_id'], row['canvas_assignment_id'] || row['id'], studentId)
                    : undefined
                }
                target="_parent"
                renderIcon={itemIcon}
              >
                {isEvent ? row['title'] : row['assignment_title']}
              </Link>
            </Text>
          </Flex.Item>
          {isEvent && !Util.dateIsFuture(row.start_at) && (
            (row.completed_at || Util.isSameUser(window.ENV.user.canvas_id, row.canvas_user_id)) && <Flex.Item padding="x-small" align='center'>
              {row.completed_at ? (
                <Pill
                  renderIcon={<IconCheckMarkSolid />}
                  color="success"
                  themeOverride={{
                    textFontWeight: '500',
                  }}
                >
                  Complete
                </Pill>
              ) : (
                <Button
                  color="success"
                  size="small"
                  onClick={() => markEventComplete(row.canvas_user_id, row.canvas_id, row.organization_name)}
                >
                  Mark Complete
                </Button>
              )}
            </Flex.Item>
          )}
        </Flex>
      </View>
    )
  }

  // Combine records and calendar events for the current day
  const combineItems = (date) => {
    const formattedDate = formatDate(date);

    const combined = [
      ...(records[formattedDate] || []).map((item) => ({
        ...item,
        sortTime: (item.submission_due_at || item.assignment_due_at || item.todo_date) ?
          new Date(item.submission_due_at || item.assignment_due_at || item.todo_date).getTime() : Infinity,
      })),
      ...(calendarEvents[formattedDate] || []).map((event) => ({
        ...event,
        item_type: 'calendar_event',
        sortTime: event.start_at ? new Date(event.start_at).getTime() : Infinity,
        id: event.canvas_id,
      })),
    ].sort((a, b) => a.sortTime - b.sortTime);

    return combined;
  }

  // Update combinedItems when relevant data changes (records, calendarEvents, startDate, endDate)
  useEffect(() => {
    const newCombinedItems = [];

    // Loop through days from startDate to endDate and compute combined items for each day
    let currentDate = new Date(startDate);
    while (currentDate <= new Date(endDate)) {
      const itemsForDay = combineItems(currentDate);
      newCombinedItems.push(itemsForDay);
      currentDate.setUTCDate(currentDate.getUTCDate() + 1); // Move to next day
    }

    setCombinedItems(newCombinedItems);
  }, [records, calendarEvents, startDate, endDate]); // Dependencies array ensures this runs on relevant changes

  const renderGrid = () => {
    return (
      <View>
        <Grid colSpacing='none' rowSpacing='small'>
          <Grid.Row>
            { [...Array(5).keys()].map(i => {
                return renderGridHeader(startDate, i)
              })
            }
          </Grid.Row>
          <Grid.Row id='items-grid'>
            { [...Array(5).keys()].map(i => {
                return (
                  <Grid.Col>{renderDayDueItem(startDate, i)}</Grid.Col>
                )
              })
            }
          </Grid.Row>
        </Grid>
      </View>
    )
  }

  return (
    <View as="div">
      { renderGrid() }
    </View>
  )
}

export default WeekItemGrid
