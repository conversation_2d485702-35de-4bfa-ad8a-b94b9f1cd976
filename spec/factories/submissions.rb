# frozen_string_literal: true

FactoryBot.define do
  factory :submission do
    sequence(:canvas_id) { |n| n }
    sequence(:canvas_course_id) { |n| n }
    sequence(:canvas_assignment_id) { |n| n }
    sequence(:canvas_user_id) { |n| n }
    submitted_at { Time.current }
    due_at { Time.current + 1.day }
    graded_at { Time.current }
    score { 85.0 }
    points_possible { 100.0 }
    excused { false }
    workflow_state { 'submitted' }
  end
end
