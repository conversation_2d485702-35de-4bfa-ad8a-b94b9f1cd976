# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ExceptionDate, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:date) }
    it { should validate_presence_of(:name) }
  end

  describe 'enums' do
    it 'defines the exception_type enum with expected values' do
      expect(described_class.exception_types).to eq({ 'holiday' => 0, 'blackout' => 1 })
    end
  end
end
