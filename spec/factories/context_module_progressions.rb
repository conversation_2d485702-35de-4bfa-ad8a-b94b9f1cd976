# frozen_string_literal: true

FactoryBot.define do
  factory :context_module_progression do
    canvas_module_id { Faker::Number.unique.number(digits: 6) }
    canvas_module_item_id { Faker::Number.unique.number(digits: 6) }
    canvas_user_id { Faker::Number.unique.number(digits: 6) }
    canvas_content_type { 'Page' }
    canvas_content_id { Faker::Number.unique.number(digits: 6) }
    canvas_content_title { Faker::Lorem.sentence }
    canvas_page_url { Faker::Internet.url }
    requirement_type { 'must_view' }
    requirement_status { 'completed' }
    lock_at { nil }
    due_at { nil }
    todo_date { nil }
  end
end
