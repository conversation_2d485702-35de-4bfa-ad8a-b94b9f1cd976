# frozen_string_literal: true

module Cross<PERSON>hardUser
  extend ActiveSupport::Concern

  included do
    has_many :user_shard_associations, primary_key: :canvas_id, foreign_key: :canvas_user_id

    after_initialize do
      @record_tenant = Apartment::Tenant.current
    end

    around_save do |_user, blk|
      raise "Attempted to save #{record.model.name} record in a different tenant!" if @record_tenant && Apartment::Tenant.current != @record_tenant

      # We could automatically switch, but I think an error is better - I don't want to encourage
      #   implicit use of any cross-tenant logic
      # switch_tenant(@record_tenant, &blk)

      blk.call
    end
  end

  class_methods do
    # TODO
  end

  def home_organization
    if canvas_id.to_i < PandaPal::Organization::SHARD_OFFSET
      PandaPal::Organization.for_apt_tenant(@record_tenant)
    else
      PandaPal::Organization.for_canvas_shard(canvas_id)
    end
  end

  # Returns an array of organizations associated with the user through user_shard_associations
  # @return [Array<Organization>] A list of organizations linked to the user
  def related_organizations
    user_shard_associations.map(&:organization)
  end

  def primary_record
    home_organization.switch_tenant do
      User.find_by(canvas_id: canvas_id.to_i % PandaPal::Organization::SHARD_OFFSET)
    end
  end

  def sharded_canvas_id
    if canvas_id.to_i < PandaPal::Organization::SHARD_OFFSET
      (PandaPal::Organization::SHARD_OFFSET * home_organization.canvas_shard_id) + canvas_id.to_i
    else
      canvas_id
    end
  end

  def shadow_record?
    canvas_id.to_i > PandaPal::Organization::SHARD_OFFSET
  end

  # rubocop:disable Style/ArgumentsForwarding
  def each_related_organization(&blk)
    return primary_record.each_related_organization(&blk) if shadow_record?
    return to_enum(:each_related_organization) unless block_given?

    home_organization.switch_tenant do
      yield home_organization
      related_organizations.each(&blk)
    end
  end
  # rubocop:enable Style/ArgumentsForwarding

  def against_shards(*args, &blk)
    method = args.length > 1 ? args.shift : :find_each
    query = args.shift

    # Detect how many arguments the block wants.
    # If it accepts 2 or more, yield both user and org.
    # If only 1, yield just the user.
    # Safely determine block arity if block exists, else default to 0
    block_arity = blk ? blk.arity : 0

    if query
      return to_enum(:against_shards, method, query) unless block_given?

      if query.is_a?(ActiveRecord::Relation)
        each_related_organization do |org|
          query.send(method) do |record|
            block_arity >= 2 ? blk.call(record, org) : blk.call(record)
          end
        end
      elsif query.is_a?(Proc)
        against_shards do |suser, org|
          query.call(suser).send(method) do |record|
            block_arity >= 2 ? blk.call(record, org) : blk.call(record)
          end
        end
      end
    else
      each_related_organization.map do |org|
        suser = primary_record.dup
        suser.canvas_id = org == home_organization ? suser.canvas_id : suser.sharded_canvas_id

        if org == current_organization
          block_arity >= 2 ? blk.call(suser, org) : blk.call(suser)
        else
          org.switch_tenant do
            block_arity >= 2 ? blk.call(suser, org) : blk.call(suser)
          end
        end
      end
    end
  end
end
