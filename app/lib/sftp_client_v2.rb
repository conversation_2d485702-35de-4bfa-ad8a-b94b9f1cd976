# frozen_string_literal: true

require 'net/sftp'

class SftpClientV2
  def initialize(endpoint, instance_names = [])
    @host = endpoint[:host]
    @user = endpoint[:user]
    @password = endpoint[:password]
    @remote_dir = endpoint[:file_path]
    @instance_names = instance_names
  end

  def fetch_files
    downloaded_files = []

    Net::SFTP.start(@host, @user, password: @password) do |sftp|
      FileUtils.mkdir_p(base_dir)

      downloaded_files = sftp.dir.entries(@remote_dir)
                             .select { |e| e.file? && e.name.end_with?('.zip') && matches_instance_name?(e.name) }
                             .map do |entry|
        local_path = base_dir.join(entry.name)
        Rails.logger.info("Downloading: #{entry.name} from #{@host}")
        sftp.download!(File.join(@remote_dir, entry.name), local_path.to_s)
        Rails.logger.info("Downloaded: #{entry.name} from #{@host}")
        local_path.to_s
      end
    end

    downloaded_files
  end

  private

  # Checks if a filename matches any of the predefined instance names.
  # If no instance names are specified, returns true for all files.
  #
  # @param filename [String] The name of the file to check
  # @return [Boolean] True if the filename matches an instance name or no instance names are set
  def matches_instance_name?(filename)
    return true if @instance_names.empty?

    @instance_names.any? { |keyword| filename.include?(keyword) }
  end

  def base_dir
    SyncDataFromSftpJob::LOCAL_DIR.join(current_organization.name, @host, @user)
  end
end
