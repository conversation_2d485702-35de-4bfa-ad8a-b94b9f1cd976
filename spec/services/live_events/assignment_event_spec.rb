# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiveEvents::AssignmentEvent, type: :service do
  let(:payload) do
    {
      body: {
        assignment_id: 5,
        context_type: 'Course',
        context_id: 12
      }
    }.with_indifferent_access
  end

  let(:service) { described_class.new }

  describe '#perform' do
    context 'when the assignment already exists' do
      let!(:course) { create(:course, canvas_id: payload[:body][:context_id]) }
      let!(:assignment) do
        create(:assignment,
               canvas_id: payload[:body][:assignment_id],
               canvas_context_id: payload[:body][:context_id],
               canvas_context_type: payload[:body][:context_type])
      end

      it 'does not create a new assignment but updates it' do
        expect { service.perform(payload) }.not_to change(Assignment, :count)
        expect(assignment.reload.canvas_context_id).to eq(payload[:body][:context_id])
      end
    end

    context 'when the assignment does not exist' do
      it 'creates a new assignment' do
        expect { service.perform(payload) }.to change(Assignment, :count).by(1)
        expect(Assignment.last.canvas_context_id).to eq(payload[:body][:context_id])
      end
    end
  end
end
