# frozen_string_literal: true

class Api::V1::UserConfigController < ApplicationController
  before_action :set_user
  before_action :set_user_config, only: [:show, :update]
  skip_authorization_check
  # authorize_resource

  def index
    @user_config = @user.user_config
    render :show, status: :ok
  end

  def create
    @user_config = @user.build_user_config(user_config_params)

    response = update_canvas_profile(@user_config.language, @user_config.timezone)
    if @user_config.save && response
      render :show, status: :created
    else
      render json: @user_config.errors, status: :unprocessable_entity
    end
  end

  def show; end

  def update
    # Build a new user config if it doesn't exist
    @user_config ||= @user.user_config

    # Handle notification tooltip updates without Canvas sync
    if params.dig(:user_config, :skip_canvas_update)
      if @user_config.update(user_config_params)
        render :show, status: :ok
      else
        render json: @user_config.errors, status: :unprocessable_entity
      end
      return
    end

    response = update_canvas_profile(user_config_params[:language], user_config_params[:timezone])
    if @user_config.update(user_config_params) && response
      render :show, status: :ok
    else
      render json: @user_config.errors, status: :unprocessable_entity
    end
  rescue StandardError => e
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  private

  def set_user_config
    @user_config = UserConfig.find_by(id: params[:id])
  end

  def user_config_params
    params.require(:user_config).permit(:canvas_user_id, :language, :home_page, :timezone, :show_notification_tooltip, :theme, :audio_enabled)
  end

  def set_user
    @user = User.find_by(canvas_id: params[:user_id])
  end

  def update_canvas_profile(language, timezone)
    user_body = { locale: language, time_zone: timezone }
    canvas_sync_client.put("/api/v1/users/#{@user.canvas_id}", { user: user_body })
  end
end
