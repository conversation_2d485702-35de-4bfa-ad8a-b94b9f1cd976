# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiveEvents::CourseEvent, type: :service do
  let(:payload) do
    {
      body: {
        course_id: 1234,
        account_id: 1234
      }
    }.with_indifferent_access
  end

  let(:service) { described_class.new }
  let(:local_course_id) { payload[:body][:course_id] }
  let(:local_account_id) { payload[:body][:account_id] }

  describe '#perform' do
    context 'when the course already exists' do
      let!(:account) { create(:account, canvas_id: local_account_id) }
      let!(:course) do
        create(:course,
               canvas_id: local_course_id,
               canvas_account_id: 1)
      end

      before do
        allow(course).to receive(:sync_from_api)
      end

      it 'does not create a new course but updates it' do
        expect { service.perform(payload) }.not_to change(Course, :count)
        expect(course.reload.canvas_account_id).to eq(local_account_id)
      end

      it 'syncs the course with API' do
        expect_any_instance_of(Course).to receive(:sync_from_api)
        service.perform(payload)
      end
    end
  end
end
