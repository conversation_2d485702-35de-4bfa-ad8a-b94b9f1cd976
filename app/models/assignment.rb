# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Assignment < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  # include CanvasSync::Concerns::LiveEventSync

  canvas_sync_features :defaults

  validates :canvas_id, uniqueness: true, presence: true
  belongs_to :context, polymorphic: true, optional: true, primary_key: :canvas_id, foreign_key: :canvas_context_id, foreign_type: :canvas_context_type
  belongs_to :course, primary_key: :canvas_id, foreign_key: :canvas_context_id, optional: true
  belongs_to :assignment_group, optional: true, primary_key: :canvas_id, foreign_key: :canvas_assignment_group_id
  has_many :submissions, primary_key: :canvas_id, foreign_key: :canvas_assignment_id

  has_many :context_module_items, as: :content, primary_key: :canvas_id, foreign_key: :canvas_content_id, foreign_type: :canvas_content_type, dependent: :destroy
  has_many :context_modules, through: :context_module_items

  api_syncable({
                 title: :name,
                 description: :description,
                 due_at: :due_at,
                 unlock_at: :unlock_at,
                 lock_at: :lock_at,
                 points_possible: :points_possible,
                 grading_type: :grading_type,
                 submission_types: ->(p) { p['submission_types'].join(',') },
                 canvas_context_id: :course_id,
                 canvas_context_type: ->(_p) { 'Course' },
                 canvas_assignment_group_id: :assignment_group_id,
                 canvas_grading_standard_id: :grading_standard_id
               }, ->(api) { api.assignment(canvas_context_id, canvas_id) })
end
