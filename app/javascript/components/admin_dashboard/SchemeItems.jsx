import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { Button } from '@instructure/ui-buttons';
import { IconSaveLine } from '@instructure/ui-icons';

import InstColorPicker from '../../shared/components/InstColorPicker';
import * as API from "../../utils/api";

const SchemeItems = (props) => {
  const { item, accountId } = props;
  const [visible, setVisible] = useState(false);
  const [colorCode, setColorCode] = useState(item['color_code'] || '')

  const handleChange = (id, newColorCode) => {
    setVisible(true);
    setColorCode(newColorCode === '#' ? null : newColorCode);
  }

  const handleCancel = () => {
    setColorCode(item['color_code']);
    setVisible(false)
  }

  const handleSave = () => {
    API.updateGradeSchemeColor(accountId, item['id'], {color_code: colorCode})
      .then((response) => response.data)
      .then((response) => {
        item['color_code'] = response['color_code']
        setVisible(false)
      })
      .catch((error) => {
        console.log(error);
        setColorCode(item['color_code']);
      });
  }

  const renderButtons = (item) => {
    return (
      <View key={"bt_"+item['id']}>
        <View padding="none small">
          <Button onClick={handleCancel}>Cancel</Button>
        </View>
        <View>
          <Button renderIcon={IconSaveLine} color="primary" onClick={handleSave}>Save</Button>
        </View>
      </View>
    )
  }

  const renderDefaultBackground = (bgcolor) => {
    return (
          <View
            as='div'
            background="secondary"
            borderRadius="medium"
            textAlign="center"
            width='5rem'
            themeOverride={{
              backgroundSecondary: bgcolor,
              color: 'white'
            }}
          >
            <Text size='x-small'>Default Background</Text>
          </View>
    )
  }

  return (
      <View as='div' key={item['id']}>
        <View as='div' display='flex'>
          <View as='div' padding="none small" width='12rem'>
            <Text weight="bold">{item['name']}</Text>
            <Text> ({item['range_limit']})%</Text>
          </View>
          {!item.color_code && renderDefaultBackground(item.default_color_code)}
        </View>
        <Flex padding="x-small small">
          <Flex.Item shouldShrink shouldGrow>
            <InstColorPicker schemeId={item['id']} colorCode={colorCode} handleChange={handleChange}/>
          </Flex.Item>
          <Flex.Item size="40%">
            { visible && renderButtons(item) }
          </Flex.Item>
        </Flex>
      </View>
  )
}

export default SchemeItems
