# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GlobalSetting, type: :model do
  let(:global_setting) { create(:global_setting) }

  describe 'validations' do
    subject { global_setting }

    it { should validate_presence_of(:setting_type) }
  end

  describe '#file' do
    subject { global_setting.file }

    it { is_expected.to be_an_instance_of(ActiveStorage::Attached::One) }
  end
end
