# frozen_string_literal: true

class ContextModuleProgression < ApplicationRecord
  belongs_to :context_module, primary_key: :canvas_id, foreign_key: :canvas_module_id, optional: true
  belongs_to :context_module_item, primary_key: :canvas_id, foreign_key: :canvas_module_item_id, optional: true

  validates :canvas_module_id, :canvas_module_item_id, :canvas_user_id, presence: true
  validates :canvas_module_id, uniqueness: { scope: [:canvas_module_item_id, :canvas_user_id] }
end
