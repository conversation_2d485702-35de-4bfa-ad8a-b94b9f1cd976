import '@/application.css'
import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';

import * as API from "../utils/api";
import GradingSchemes from './admin_dashboard/GradingSchemes';

const AccountAdminDashboard = (props) => {
  const canvasAccountId = window.ENV.canvas_account_id;
  const [gradingSchemeColors, setGradingSchemeColors] = useState({});

  useEffect(() => {
    const originalBg = document.body.style.backgroundColor;
    // Apply new background color
    document.body.style.backgroundColor = '#FFFFFF';
    document.documentElement.style.backgroundColor = '#FFFFFF';

    return () => {
      // Reset background when component unmounts
      document.body.style.backgroundColor = originalBg;
      document.documentElement.style.backgroundColor = originalBg;
    };
  }, []);

  useEffect(() => {
    getGradingSchemeColorsList()
  }, []);

  const getGradingSchemeColorsList = () => {
    API.getGradeSchemeColors(canvasAccountId)
      .then((response) => response.data)
      .then((response) => {
        setGradingSchemeColors(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const renderGradingSchemeColors = (scheme_type) => {
    let title = `${scheme_type} Grading Scheme Colors`;
    return (
      <View>
        <View><Text transform='uppercase' weight="light" letterSpacing="expanded">{title}</Text></View>
        <View>{gradingSchemeColors && <GradingSchemes schemeType={scheme_type} records={gradingSchemeColors[scheme_type]} accountId={canvasAccountId} />}</View>
      </View>
    )
  }

  return (
    <View as="div" padding="medium">
      <Flex direction='column'>
        <Flex.Item>
          <View><Text size="large" weight="bold">K-5 Dashboard Account Settings</Text></View>
        </Flex.Item>
        <Flex.Item  padding="medium none">
          { gradingSchemeColors && renderGradingSchemeColors('standard') }
        </Flex.Item>
        <Flex.Item>
          { gradingSchemeColors && renderGradingSchemeColors('mastery') }
        </Flex.Item>
      </Flex>
    </View>
  )
}

export default AccountAdminDashboard
