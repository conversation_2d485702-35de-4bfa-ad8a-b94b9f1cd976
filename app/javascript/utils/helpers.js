import moment from "moment";

// TODO: Deprecate `canvasAssignmentUrl` in favor of `handleCanvasAssignmentUrlNavigation`
export const canvasAssignmentUrl = (canvas_assignment_path, student_id) => {
  let canvas_url = window.location.ancestorOrigins[0] || window.ENV.canvas_url;
  return new URL(`/${canvas_assignment_path}?student_id=${student_id}&show_back_to_dashboard_link=true`, canvas_url).href;
}

export const canvasCalendarEventUrl = (canvas_calendar_event_path) => {
  let canvas_url = window.location.ancestorOrigins[0] || window.ENV.canvas_url;
  return new URL(`/${canvas_calendar_event_path}`, canvas_url).href;
}

// Helper function to generate item canvas URL from course_content_path
export const getItemCanvasUrl = (path, studentId) => {
  return path ? canvasAssignmentUrl(path, studentId) : '#';
}

export const handleCanvasAssignmentUrlNavigation = (assignment_url, course_id, assignment_id, student_id) => {
  return (event) => {
    // Prevent default navigation
    event.preventDefault();

    // Send message to parent window
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({
        action: 'k5launchlink',
        course_id: course_id,
        assignment_id: assignment_id,
        student_id: student_id
      }, '*');
    }

    // Navigate after a small delay
    setTimeout(() => {
      window.parent.location.href = assignment_url;
    }, 50);
  };
};

// HACK: Compare canvas user with sharded user id
// TODO: Please change this to better solution as this `end_with?` can break things with specific use cases
export const isSameUser = (userId, canvasUserId) => {
  const uid = String(userId);
  const cid = String(canvasUserId);

  return uid === cid ||
         uid.endsWith(cid) ||
         cid.endsWith(uid);
}

// Format datestring in MM-DD-YYYY
export const formatDateString = (dateString) => {
  if (dateString == null) {
    return '-'
  } else {
    let userTZ = window.ENV.current_time_zone;
    return userTZ ? moment(dateString).tz(userTZ).format('MM-DD-YYYY') : moment.utc(dateString).format('MM-DD-YYYY');
  }
}

export const setDateOnZeroHour = (dateStr) => {
  let date = new Date(dateStr);
  date.setHours(0,0,0,0);
  return date;
}

export const isBlackoutDate = (() => {
  let blackoutSet = null; // Cache variable

  return (inputDate) => {
    if (!blackoutSet) {
      blackoutSet = new Set(window.ENV?.blackout_dates?.map(date => toUTCDateString(new Date(date))) || []);
    }

    return blackoutSet.has(new Date(inputDate).toDateString());
  };
})();

export const dateIsFuture = (date) => {
  const inputDate = new Date(date).toDateString();
  const today = new Date().toDateString();
  return new Date(inputDate) > new Date(today);
}

// converting date in .toDateString() format, without the timezone shift
function toUTCDateString(date) {
    const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    const dayName = days[date.getUTCDay()];
    const monthName = months[date.getUTCMonth()];
    const day = String(date.getUTCDate()).padStart(2, '0'); // Pad single digits with 0
    const year = date.getUTCFullYear();

    return `${dayName} ${monthName} ${day} ${year}`;
}

export const isWeekend = (dateStr) => {
  // Match day name at the beginning of the string
  const match = dateStr.match(/^(Sun|Sat)/i);

  return match !== null;
}
