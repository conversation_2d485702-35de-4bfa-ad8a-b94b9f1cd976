import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { DateInput2 } from '@instructure/ui-date-input';
import { IconButton } from '@instructure/ui-buttons';
import { IconArrowOpenEndLine, IconArrowOpenStartLine } from '@instructure/ui-icons';
import { Spinner } from '@instructure/ui-spinner';

import * as API from "../../utils/api";
import * as Util from "../../utils/helpers";
import * as GlobalConstant from '../../utils/constants';
import WeekItemGrid from './WeekItemGrid';

const WeekView = (props) => {
  const { studentId } = props;
  const [dueAssignments, setDueAssignments] = useState([]);
  const [calendarEvents, setCalendarEvents] = useState({});
  const [inputDate, setInputDate] = useState(new Date().toDateString());
  const [dateRange, setDateRange] = useState();
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();

  const [prevInteraction, setPrevInteraction] = useState('enabled');
  const [nextInteraction, setNextInteraction] = useState('enabled');
  const [minDate, setMinDate] = useState();
  const [maxDate, setMaxDate] = useState();
  const [dateError, setDateError] = useState([]);
  const [isReady, setIsReady] = useState();

  const [flag, setFlag] = useState(false);  // State to hold the flag to calendar events & due assignments
  const [isLoading, setIsLoading] = useState(false); // Loading state

  useEffect(() => {
    if (flag) {
      const fetchData = async () => {
        setIsLoading(true);
        try {
          await Promise.all([
            getStudentCalendarEvents(),
            getWeeklyDueAssignments()
          ]);
        } catch (error) {
          console.error('Error during API calls:', error);
        } finally {
          setIsLoading(false);
          setFlag(false);
        }
      };

      fetchData();
    }
  }, [flag]);

  useEffect(() => {
    const fetchInitialData = async () => {
      setIsLoading(true);
      try {
        await Promise.all([
          getStudentCalendarEvents(),
          getWeeklyDueAssignments()
        ]);
        setDateRange(weekRange(inputDate));
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, [studentId, inputDate]);

  const getWeeklyDueAssignments = async () => {
    let due_date = inputDate;

    return API.getStudentWeeklyDueAssignments(studentId, { due_date: due_date })
      .then((response) => response.data)
      .then((response) => {
        setDueAssignments(response['data']);
        setStartDate(weekStartDate(inputDate));
        setEndDate(weekEndDate(response['end_date']));
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const getStudentCalendarEvents = async () => {
    let due_date = inputDate;

    return API.getStudentCalendarEvents(studentId, { due_date: due_date, weekly: true })
      .then((response) => response.data)
      .then((response) => {
        // Extracts the values from the response data object. The object contains key-value pairs, where the key is the date
        // and the value is an array of events for that specific date.
        setCalendarEvents(response.data);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  useEffect(() => {
    getDateRangeLimits();
  }, [studentId]);

  const getDateRangeLimits = () => {
    API.getStudentDateLimits(studentId)
      .then((response) => response.data)
      .then((response) => {
        let start = [response['course_start'], response['term_start']]
          .filter(n => n)
          .map(parsedDate);

        let end = [response['course_end'], response['term_end']]
          .filter(n => n)
          .map(parsedDate);

        let minimumDate = Math.min(...start)
        let maximumDate = Math.max(...end)

        setMinDate(minimumDate ? parsedDate(minimumDate) : null)
        setMaxDate(maximumDate ? parsedDate(maximumDate) : null)
      })
      .catch((error) => {
        console.log(error);
      });
  }

  useEffect(() => {
    if (minDate && maxDate) {
      calendarInteraction(inputDate);
      setIsReady(true);
    }
  }, [minDate, maxDate]);

  const parsedDate = (dateStr) => {
    return new Date(dateStr);
  }

  const weekStartDate = (dateStr) => {
    const newDate = parsedDate(dateStr)

    let day = newDate.getDay(); // 0 (Sunday) to 6 (Saturday)
    let diff = day === 0 ? -6 : 1 - day; // Adjust so Monday is the start
    newDate.setDate(newDate.getDate() + diff);

    return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate() - newDate.getDay() + 1)
  }

  const weekEndDate = (dateStr) => {
    const newDate = weekStartDate(dateStr)
    newDate.setUTCDate(newDate.getUTCDate() + 4);
    return newDate
  }

  const modifyDay = (dateStr, type, step) => {
    let d = new Date(dateStr);
    d.setUTCDate(d.getUTCDate() + step);
    setInputDate(d.toDateString())
    calendarInteraction(d)
  }

  const weekRange = (dateStr) => {
    let weekStart = weekStartDate(dateStr);
    let weekEnd = weekEndDate(dateStr)

    let wsMonth = GlobalConstant.MONTH_NAMES[weekStart.getMonth()];
    let wsDay = `${weekStart.getDate()}`.padStart(2, '0');

    let weMonth = GlobalConstant.MONTH_NAMES[weekEnd.getMonth()];
    let weDay = `${weekEnd.getDate()}`.padStart(2, '0');
    let weYear = weekEnd.getFullYear();

    return `${wsMonth} ${wsDay} - ${weMonth} ${weDay}, ${weYear}`
  }

  const calendarInteraction = (dateStr) => {
    let date = parsedDate(dateStr);

    if (date.getTime() < minDate.getTime()) {
      setPrevInteraction('disabled')
      setNextInteraction('enabled')
      setDateError([{
        type: 'error',
        text: 'Date is outside of the course & term start date.'
      }])
    } else if (date.getTime() > maxDate.getTime()) {
      setPrevInteraction('enabled')
      setNextInteraction('disabled')
      setDateError([{
        type: 'error',
        text: 'Date is outside of the course & term end date.'
      }])
    } else {
      setPrevInteraction('enabled')
      setNextInteraction('enabled')
      setDateError([])
    }
  }

  const handleDateValidation = (e, inputValue, utcIsoDate) => {
    const date = new Date(utcIsoDate)
    calendarInteraction(date)
  }

  const renderDatePicker = () => {
    return (
      <Flex alignItems="start">
        <Flex.Item>
          <IconButton
            screenReaderLabel="Prev"
            onClick={() => { modifyDay(inputDate, 'day', -7) }}
            interaction={prevInteraction}
            margin="none xx-small"
            focusColor='inverse'
          >
            <IconArrowOpenStartLine />
          </IconButton>
        </Flex.Item>
        <Flex.Item>
          <DateInput2
            renderLabel=""
            screenReaderLabels={{
              calendarIcon: 'Calendar',
              nextMonthButton: 'Next month',
              prevMonthButton: 'Previous month'
            }}
            value={dateRange}
            width="20rem"
            locale="en-us"
            dateFormat={{
              parser: (input) => {
                const [sm, sd, em, ed, ey] = input.split(/[,.\s/.-]+/)
                const newDate = weekStartDate(`${em} ${ed}, ${ey}`)
                return isNaN(newDate) ? '' : newDate
              },
              formatter: (newDateF) => {
                return weekRange(newDateF)
              }
            }}
            onChange={(e, inputValue, utcDateString) => {
              let newDate = parsedDate(utcDateString);
              setInputDate(newDate.toDateString())
              setDateRange(weekRange(newDate))
            }}
            invalidDateErrorMessage="Invalid date"
            onRequestValidateDate={handleDateValidation}
            messages={dateError}
          />
        </Flex.Item>
        <Flex.Item>
          <IconButton
            screenReaderLabel="Next"
            onClick={() => { modifyDay(inputDate, 'day', 7) }}
            interaction={nextInteraction}
            margin="none xx-small"
            focusColor='inverse'
          >
            <IconArrowOpenEndLine />
          </IconButton>
        </Flex.Item>
      </Flex>
    )
  }

  return (
    <View as="div">
      <View as="div" display='block' padding='small' textAlign='start'>
        {isReady && renderDatePicker()}
      </View>
      <View as="div" display='block' padding='small'>
        {isLoading ? (
          <Flex justifyItems="center" alignItems="center" height="10rem">
            <Spinner renderTitle="Loading" />
          </Flex>
        ) : (
          <WeekItemGrid
            studentId={studentId}
            records={dueAssignments}
            startDate={startDate}
            endDate={endDate}
            calendarEvents={calendarEvents}
            setFlag={setFlag}
          />
        )}
      </View>
    </View>
  )
}

export default WeekView;
