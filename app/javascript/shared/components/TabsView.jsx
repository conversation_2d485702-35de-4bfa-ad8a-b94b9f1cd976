import { useState } from 'react';

import { Tabs } from '@instructure/ui-tabs';
import { View } from '@instructure/ui-view';

const TabsView = (props) => {
  const { tabs, defaultIndex } = props;
  const [selectedIndex, setSelectedIndex] = useState(defaultIndex);

  const handleTabChange = (data) => {
    setSelectedIndex(data.index)
  }

  return (
    <View>
      <Tabs
        margin='small 0'
        padding='0'
        onRequestTabChange={(event, data) => handleTabChange(data)}
      >
        {tabs.map((tab, index) => (
          <Tabs.Panel
            key={tab.id}
            id={tab.id}
            renderTitle={tab.title}
            textAlign='center'
            padding='xx-small 0 0 0'
            isSelected={selectedIndex === index}
          >
            {tab.component}
          </Tabs.Panel>
        ))}
      </Tabs>
    </View>
  )
}

export default TabsView
