import React from "react";
import { Select } from "@instructure/ui-select";
import { IconUserLine } from "@instructure/ui-icons";

export default class StudentSelect extends React.Component {
  state = {
    inputValue: this.props.inputValue,
    isShowingOptions: false,
    highlightedOptionId: null,
    selectedOptionId: this.props.selectedOptionId,
    filteredOptions: this.props.options,
    announcement: null,
  };

  getOptionById(queryId) {
    return this.props.options.find(({ id }) => id === queryId);
  }

  getOptionsChangedMessage(newOptions) {
    let message =
      newOptions.length !== this.state.filteredOptions.length
        ? `${newOptions.length} options available.` // options changed, announce new total
        : null; // options haven't changed, don't announce
    if (message && newOptions.length > 0) {
      // options still available
      if (this.state.highlightedOptionId !== newOptions[0].id) {
        // highlighted option hasn't been announced
        const option = this.getOptionById(newOptions[0].id).name;
        message = `${option}. ${message}`;
      }
    }
    return message;
  }

  filterOptions = (value) => {
    return this.props.options.filter((option) =>
      option.name.toLowerCase().startsWith(value.toLowerCase())
    );
  };

  matchValue() {
    const {
      filteredOptions,
      inputValue,
      highlightedOptionId,
      selectedOptionId,
    } = this.state;

    // an option matching user input exists
    if (filteredOptions.length === 1) {
      const onlyOption = filteredOptions[0];
      // automatically select the matching option
      if (onlyOption.name.toLowerCase() === inputValue.toLowerCase()) {
        return {
          inputValue: onlyOption.name,
          selectedOptionId: onlyOption.id,
          filteredOptions: this.filterOptions(""),
        };
      }
    }
    // allow user to return to empty input and no selection
    if (inputValue.length === 0) {
      return { selectedOptionId: null };
    }
    // no match found, return selected option label to input
    if (selectedOptionId) {
      const selectedOption = this.getOptionById(selectedOptionId);
      return { inputValue: selectedOption.name };
    }
    // input value is from highlighted option, not user input
    // clear input, reset options
    if (highlightedOptionId) {
      if (inputValue === this.getOptionById(highlightedOptionId).name) {
        return {
          inputValue: "",
          filteredOptions: this.filterOptions(""),
        };
      }
    }
  }

  handleShowOptions = (event) => {
    this.setState(({ filteredOptions }) => ({
      isShowingOptions: true,
      announcement: `List expanded. ${filteredOptions.length} options available.`,
    }));
  };

  handleHideOptions = (event) => {
    const { selectedOptionId, inputValue } = this.state;
    this.setState({
      isShowingOptions: false,
      highlightedOptionId: null,
      announcement: "List collapsed.",
      ...this.matchValue(),
    });
  };

  handleBlur = (event) => {
    this.setState({ highlightedOptionId: null });
  };

  handleHighlightOption = (event, { id }) => {
    event.persist();
    const option = this.getOptionById(id);
    if (!option) return; // prevent highlighting of empty option
    this.setState((state) => ({
      highlightedOptionId: id,
      inputValue: event.type === "keydown" ? option.name : state.inputValue,
      announcement: option.name,
    }));
  };

  handleSelectOption = (event, { id }) => {
    const option = this.getOptionById(id);
    if (!option) return; // prevent selecting of empty option
    this.setState({
      selectedOptionId: id,
      inputValue: option.name,
      isShowingOptions: false,
      filteredOptions: this.props.options,
      announcement: `${option.name} selected. List collapsed.`,
    });

    this.props.onChange(id);
  };

  handleInputChange = (event) => {
    const value = event.target.value;
    const newOptions = this.filterOptions(value);
    this.setState((state) => ({
      inputValue: value,
      filteredOptions: newOptions,
      highlightedOptionId: newOptions.length > 0 ? newOptions[0].id : null,
      isShowingOptions: true,
      selectedOptionId: value === "" ? null : state.selectedOptionId,
      announcement: this.getOptionsChangedMessage(newOptions),
    }));
    this.props.onChange(value === "" ? null : this.state.selectedOptionId);
  };

  render() {
    const {
      inputValue,
      isShowingOptions,
      highlightedOptionId,
      selectedOptionId,
      filteredOptions,
      announcement,
    } = this.state;

    return (
      <div>
        <Select
          renderLabel={this.props.label}
          assistiveText="Type or use arrow keys to navigate options."
          placeholder={this.props.placeholder || "Search..."}
          inputValue={inputValue}
          isShowingOptions={isShowingOptions}
          onBlur={this.handleBlur}
          onRequestShowOptions={this.handleShowOptions}
          onRequestHideOptions={this.handleHideOptions}
          onRequestHighlightOption={this.handleHighlightOption}
          onRequestSelectOption={this.handleSelectOption}
          renderBeforeInput={<IconUserLine inline={false} />}
        >
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option) => {
              return (
                <Select.Option
                  id={option.id}
                  key={option.id}
                  isHighlighted={option.id === highlightedOptionId}
                  isSelected={option.id === selectedOptionId}
                  isDisabled={option.disabled}
                  renderBeforeLabel={IconUserLine}
                >
                  {option.name}
                </Select.Option>
              );
            })
          ) : (
            <Select.Option id="empty-option" key="empty-option">
              ---
            </Select.Option>
          )}
        </Select>
      </div>
    );
  }
}
