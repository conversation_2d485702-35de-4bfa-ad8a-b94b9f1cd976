# frozen_string_literal: true

class ContextModuleReportProcessor < CanvasSync::Processors::ReportProcessor
  DEFAULT_BATCH_SIZE = 10_000

  MAPPING = {
    report_columns: {
      canvas_id: {
        report_column: :canvas_context_module_id,
        type: :integer
      },
      canvas_context_id: {
        report_column: :canvas_context_id,
        type: :integer
      },
      canvas_context_type: {
        report_column: :canvas_context_type,
        type: :string
      },
      position: {
        report_column: :position,
        type: :integer
      },
      name: {
        report_column: :name,
        type: :string
      },
      workflow_state: {
        report_column: :workflow_state,
        type: :string
      },
      deleted_at: {
        report_column: :deleted_at,
        type: :datetime
      },
      unlock_at: {
        report_column: :unlock_at,
        type: :datetime
      },
      completion_requirements: {
        report_column: :completion_requirements,
        type: :jsonb
      },
      prerequisites: {
        report_column: :prerequisites,
        type: :jsonb
      }
    }
  }.with_indifferent_access.freeze

  def self.process(report_file_path, _options, _report_id)
    new(report_file_path)
  end

  # rubocop:disable Lint/MissingSuper
  def initialize(report_file_path)
    import(report_file_path)
  end
  # rubocop:enable Lint/MissingSuper

  private

  def import(report_file_path)
    database_column_names = MAPPING[:report_columns].keys

    update_conditions = {
      columns: database_column_names,
      condition: condition_sql(ContextModule, database_column_names),
      conflict_target: [:canvas_id],
      index_name: :index_context_modules_on_canvas_id
    }
    options = { validate: false, on_duplicate_key_update: update_conditions }

    rows = []
    CSV.foreach(report_file_path, headers: true, header_converters: :symbol) do |row|
      next if row.nil?

      rows << MAPPING[:report_columns].map do |_db_col, csv_column|
        value = nil
        value = row[csv_column[:report_column]] if csv_column[:report_column]
        value = YAML.load(value) if csv_column[:type].to_sym == :jsonb
        value
      end

      if rows.length >= batch_size
        ContextModule.import(database_column_names, rows, options)
        rows = []
      end
    end

    ContextModule.import(database_column_names, rows, options)
  end

  def batch_size
    batch_size = ENV['BULK_IMPORTER_BATCH_SIZE'].to_i
    batch_size.positive? ? batch_size : DEFAULT_BATCH_SIZE
  end

  def condition_sql(klass, columns)
    columns_str = columns.map { |c| "#{klass.quoted_table_name}.#{c}" }.join(', ')
    excluded_str = columns.map { |c| "EXCLUDED.#{c}" }.join(', ')
    "(#{columns_str}) IS DISTINCT FROM (#{excluded_str})"
  end
end
