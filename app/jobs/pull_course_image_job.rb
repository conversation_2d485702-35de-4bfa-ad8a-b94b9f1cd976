# frozen_string_literal: true

class PullCourseImageJob < CanvasSync::Job
  queue_as :critical

  def perform(options = {})
    updated_after = nil
    if options[:updated_after] == true
      last_batch = CanvasSync::SyncBatch.where(status: 'completed', batch_genre: 'default').last
      updated_after = last_batch&.started_at&.iso8601
    end

    courses = Course.with_only_k5_students
    courses.each do |course|
      # don't do API call if image is already present and course is not updated recently
      next if course.image_url.present? && (updated_after.present? && course.updated_at < updated_after)

      pull_and_update_image(course)
    end
  rescue StandardError => e
    Rails.logger.error "Error: #{e.message}"
    Rails.logger.error e.backtrace
    raise e
  end

  def pull_and_update_image(course)
    response = canvas_sync_client.course(course.canvas_id, { 'include[]': 'course_image' })
    course.image_url = response['image_download_url']
    course.save!
  rescue StandardError => e
    Rails.logger.error "Error: #{e.message}"
    Rails.logger.error e.backtrace
  end
end
