# frozen_string_literal: true

class ScoreProcessor < CanvasSync::Processors::ReportProcessor
  DEFAULT_BATCH_SIZE = 10_000

  def self.process(report_file_path, _options, _report_id)
    new(report_file_path)
  end

  # rubocop:disable <PERSON><PERSON>/MissingSuper
  def initialize(report_file_path)
    @columns = [:canvas_enrollment_id, :current_score, :unposted_current_score, :final_score, :current_letter_grade, :unposted_letter_grade, :final_letter_grade, :override_score, :workflow_state]
    import(report_file_path)
  end
  # rubocop:enable Lint/MissingSuper

  private

  def import(report_file_path)
    update_conditions = {
      columns: @columns,
      condition: condition_sql(Score, @columns),
      conflict_target: [:canvas_enrollment_id],
      index_name: :score_on_enrollment
    }
    options = { validate: false, on_duplicate_key_update: update_conditions }

    rows = []
    CSV.foreach(report_file_path, headers: true, header_converters: :symbol) do |row|
      next if row.nil?

      rows << @columns.map do |column|
        row[column]
      end

      if rows.length >= batch_size
        Score.import(@columns, rows, options)
        rows = []
      end
    end

    Score.import(@columns, rows, options)
  end

  def batch_size
    batch_size = ENV['BULK_IMPORTER_BATCH_SIZE'].to_i
    batch_size.positive? ? batch_size : DEFAULT_BATCH_SIZE
  end

  # This method generates SQL that looks like:
  # (users.sis_id, users.email) IS DISTINCT FROM (EXCLUDED.sis_id, EXCLUDED.email)
  #
  # This prevents activerecord-import from setting the `updated_at` column for
  # rows that haven't actually changed. This allows you to query for rows that have changed
  # by doing something like:
  #
  # started_at = Time.now
  # run_the_users_sync!
  # changed = User.where("updated_at >= ?", started_at)
  def condition_sql(klass, columns)
    columns_str = columns.map { |c| "#{klass.quoted_table_name}.#{c}" }.join(', ')
    excluded_str = columns.map { |c| "EXCLUDED.#{c}" }.join(', ')
    "(#{columns_str}) IS DISTINCT FROM (#{excluded_str})"
  end
end
