# frozen_string_literal: true

class Api::V1::GlobalSettingsController < ApplicationController
  include Rails.application.routes.url_helpers

  def index
    authorize! :read, :global_settings
    @global_settings = GlobalSetting.all
    @global_settings = @global_settings.where(setting_type: params[:setting_type]) if params[:setting_type].present?
  end

  def create
    authorize! :create, :global_settings
    @global_setting = GlobalSetting.new(upload_params)
    @global_setting.file.attach(upload_params[:file])
    @global_setting.save!

    render json: json_response, status: :ok
  rescue StandardError => e
    render json: {
      message: e.message
    }, status: :unprocessable_entity
  end

  def update
    authorize! :create, :global_settings
    @global_setting = GlobalSetting.find(params[:id])
    @global_setting.file.attach(upload_params[:file])
    @global_setting.save!

    render json: json_response, status: :ok
  rescue StandardError => e
    render json: {
      message: e.message
    }, status: :unprocessable_entity
  end

  def destroy
    authorize! :destroy, :global_settings
    @global_setting = GlobalSetting.find(params[:id])
    @global_setting.destroy
  end

  private

  def upload_params
    params.require(:upload).permit(:file, :setting_type, :settings)
  end

  def json_response
    {
      id: @global_setting.id,
      setting_type: @global_setting.setting_type,
      settings: @global_setting.settings,
      file_name: @global_setting.file.filename,
      file_url: rails_blob_url(@global_setting.file, only_path: true)
    }
  end
end
