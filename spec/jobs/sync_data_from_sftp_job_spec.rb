# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SyncDataFromSftpJob, type: :job do
  let(:endpoint) { { host: 'sftp.example.com', user: 'user', password: 'password' } }
  let(:organization) do
    current_organization.tap do |org|
      org.settings[:sftp_endpoints] = [endpoint]
    end
  end
  let(:job) { described_class.new }

  before do
    allow(job).to receive(:current_organization).and_return(organization)
  end

  describe '#perform' do
    context 'when sftp_endpoints are not configured' do
      let(:organization) do
        current_organization.tap do |org|
          org.settings[:sftp_endpoints] = nil
        end
      end

      it 'logs an error and returns' do
        expect(Rails.logger).to receive(:error).with("SFTP settings not configured for #{organization.name}")
        job.perform
      end
    end

    context 'when sftp_endpoints are configured' do
      it 'processes each endpoint' do
        expect(job).to receive(:process_sftp_endpoint).with(endpoint)
        job.perform
      end
    end
  end

  describe '#process_sftp_endpoint' do
    let(:sftp_client) { instance_double(SftpClientV2) }
    let(:zip_file) { 'path/to/zip_file.zip' }

    before do
      allow(SftpClientV2).to receive(:new).and_return(sftp_client)
      allow(sftp_client).to receive(:fetch_files).and_return([zip_file])
      allow(job).to receive(:process_zip)
    end

    it 'fetches files from the SFTP endpoint and processes them' do
      expect(sftp_client).to receive(:fetch_files)
      expect(job).to receive(:process_zip).with(zip_file, endpoint[:host], endpoint[:user])
      job.send(:process_sftp_endpoint, endpoint)
    end

    it 'logs an error if an exception occurs' do
      allow(sftp_client).to receive(:fetch_files).and_raise(StandardError.new('error'))
      expect { job.send(:process_sftp_endpoint, endpoint) }.to raise_error(StandardError)
    end
  end

  describe '#process_zip' do
    let(:zip_file) { 'path/to/zip_file.zip' }
    let(:csv_file) { 'path/to/users.csv' }

    before do
      allow(ZipExtractor).to receive(:extract).and_return([csv_file])
      allow(FileUtils).to receive(:rm_f)
      allow(job).to receive(:process_csv)
      allow(File).to receive(:exist?).and_return(true)
    end

    it 'extracts files from the zip and processes them' do
      expect(ZipExtractor).to receive(:extract)
      expect(job).to receive(:process_csv).with(csv_file)
      job.send(:process_zip, zip_file, endpoint[:host], endpoint[:user])
    end

    it 'deletes the extracted files and the zip file' do
      expect(FileUtils).to receive(:rm_f)
      job.send(:process_zip, zip_file, endpoint[:host], endpoint[:user])
    end
  end

  describe '#process_csv' do
    let(:csv_file) { 'path/to/users.csv' }

    it 'processes users CSV file' do
      expect(job).to receive(:process_users_csv).with(csv_file)
      job.send(:process_csv, csv_file)
    end

    it 'processes courses CSV file' do
      csv_file = 'path/to/classes.csv'
      expect(job).to receive(:process_courses_csv).with(csv_file)
      job.send(:process_csv, csv_file)
    end
  end

  describe 'SftpClientV2' do
    let(:endpoint) { { host: 'sftp.example.com', user: 'test_user', password: 'password', file_path: '/remote/path' } }
    let(:sftp_client_v2) { SftpClientV2.new(endpoint) }
    let(:sftp_session) { instance_double(Net::SFTP::Session) }
    let(:sftp_dir) { instance_double(Net::SFTP::Operations::Dir) }
    let(:remote_file) { double('remote_file', name: 'test.zip', file?: true) }
    let(:base_dir) do
      SyncDataFromSftpJob::LOCAL_DIR.join(current_organization.name, endpoint[:host], endpoint[:user])
    end
    let(:local_path) { base_dir.join('test.zip') }

    before do
      allow(Net::SFTP).to receive(:start).with(endpoint[:host], endpoint[:user],
                                               password: endpoint[:password]).and_yield(sftp_session)
      allow(sftp_session).to receive(:dir).and_return(sftp_dir)
      allow(sftp_dir).to receive(:entries).and_return([remote_file])
      allow(remote_file).to receive(:name).and_return('test.zip')
      allow(remote_file).to receive(:file?).and_return(true)
      allow(sftp_session).to receive(:download!).with(
        File.join(endpoint[:file_path], remote_file.name), local_path.to_s
      )
      allow(FileUtils).to receive(:mkdir_p).with(base_dir)
    end

    it 'fetches files from SFTP' do
      expect(sftp_client_v2.fetch_files).to eq([local_path.to_s])
    end
  end
end
