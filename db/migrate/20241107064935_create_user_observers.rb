# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateUserObservers < ActiveRecord::Migration[5.1]
  def change
    create_table :user_observers do |t|
      t.bigint :observing_user_id
      t.bigint :observed_user_id
      t.string :workflow_state

      t.timestamps
    end

    add_index :user_observers, [:observed_user_id, :observing_user_id], unique: true
    add_index :user_observers, :observing_user_id
    add_index :user_observers, :observed_user_id
  end
end
