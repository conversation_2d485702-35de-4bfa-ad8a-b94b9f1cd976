# frozen_string_literal: true

class Api::V1::AnnouncementsController < ApplicationController
  skip_authorization_check
  before_action :set_user

  skip_before_action :forbid_access_if_lacking_session, only: [:index, :destroy]

  def index
    account_id = current_organization.canvas_account_id

    params = { as_user_id: @user.canvas_id }

    begin
      @announcements = canvas_sync_client
                       .get("/api/v1/accounts/#{account_id}/account_notifications", params)
                       .all_pages!
                       .to_a

      render json: @announcements
    rescue StandardError => e
      Rails.logger.error("Error fetching account notifications for user #{@user.canvas_id} in account #{account_id}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      render json: { error_message: e.message }, status: :unprocessable_entity
    end
  end

  def destroy
    account_id = current_organization.canvas_account_id
    announcement_id = params[:id]

    begin
      canvas_sync_client.delete("/api/v1/accounts/#{account_id}/account_notifications/#{announcement_id}",
                                { as_user_id: @user.canvas_id })

      head :ok
    rescue StandardError => e
      Rails.logger.error("Error dismissing announcement #{announcement_id} for user #{@user.canvas_id}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      render json: { error_message: e.message }, status: :unprocessable_entity
    end
  end

  private

  def set_user
    @user = User.find_by!(canvas_id: params[:user_id])
  rescue ActiveRecord::RecordNotFound
    render json: { error_message: 'User not found' }, status: :not_found
  end
end
