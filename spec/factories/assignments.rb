# frozen_string_literal: true

FactoryBot.define do
  factory :assignment do
    canvas_id { Faker::Number.unique.number(digits: 5) }
    canvas_context_id { Faker::Number.unique.number(digits: 5) }
    canvas_context_type { 'Course' }
    title { Faker::Lorem.word }
    description { Faker::Lorem.word }
    due_at { Time.now.utc + 3.days }
    points_possible { 100 }
    workflow_state { 'published' }
  end
end
