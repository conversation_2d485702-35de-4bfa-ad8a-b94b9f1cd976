import { useEffect, useState } from 'react';
import { Table } from '@instructure/ui-table';
import { Responsive } from '@instructure/ui-responsive';

const InstTable = ({ headers, rows }) => {
  return (
    <Responsive
      query={{
        small: { maxWidth: '40rem' },
        large: { minWidth: '41rem' }
      }}
      props={{
        small: { layout: 'stacked' },
        large: { layout: 'fixed' }
      }}
    >
      {({ layout }) => (
        <div>
          <Table caption="custom table caption" layout={layout} hover={true}>
            <Table.Head>
              <Table.Row>
                {(headers || []).map(({ id, text, width, textAlign }) => (
                  <Table.ColHeader
                    key={id}
                    id={id}
                    width={width}
                    textAlign={textAlign}
                  >
                    {text}
                  </Table.ColHeader>
                ))}
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {rows.map((row) => (
                <Table.Row key={row.id}>
                  {headers.map(({ id, renderCell, textAlign }) => (
                    <Table.Cell
                      key={id}
                      textAlign={layout === 'stacked' ? 'start' : textAlign}
                    >
                      {renderCell ? renderCell(row, layout) : row[id]}
                    </Table.Cell>
                  ))}
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
      )}
    </Responsive>
  )
}

export default InstTable
