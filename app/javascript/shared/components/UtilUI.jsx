

import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Badge } from '@instructure/ui-badge';
import { Alert } from '@instructure/ui-alerts';
import { IconInfoLine } from '@instructure/ui-icons';

export const CounterCircle = (props) => {
  const { counts } = props;
  return (
    <View
      display="inline-block"
      borderRadius="circle"
      as="span"
      background="primary"
      borderWidth="small"
      textAlign="center"
      height="1.5rem"
      width="1.5rem"
      position="relative"
      {...props}
      >
        <Text weight="bold" size="small">{counts}</Text>
      </View>
  )
}

export const IconCircle = (props) => {
  const { renderIcon } = props;
  return (
    <View
      display="inline-block"
      borderRadius="circle"
      as="span"
      background="primary"
      borderWidth="small"
      textAlign="center"
      height="1.8rem"
      width="1.8rem"
      position="relative"
      {...props}
      >
        <Text size="x-small">{renderIcon}</Text>
      </View>
  )
}

export const TextWithBadgeCount = (props) => {
  const { title, count, variant = 'danger' } = props;
  return (
    <View className='header-text'>
      <Badge
        count={count}
        variant={variant}
        >
        <View as="div" padding="x-small none">{title}</View>
      </Badge>
    </View>
  )
}

export const TabTitle = (props) => {
  const { title, isSelected } = props;
  return (
    <View
      as="div"
      padding="x-small small"
      className={`agenda-tab-title ${isSelected ? 'agenda-tab-title-selected' : 'agenda-tab-title-unselected'}`}
    >
      {title}
    </View>
  )
}

export const InfoAlert = (props) => {
  const { title, subText } = props;
  const boldText = title ? <Text size="medium" weight="bold">{title}&nbsp;</Text> : null;
  return (
    <Alert
      variant="info"
      margin="small"
      screenReaderLabel="Info Alert"
      renderCustomIcon={() => <IconInfoLine />}
      {...props}
      themeOverride={{
        infoBorderColor: '#1658C9',
        infoIconBackground: '#1658C9'
      }}
    >
      {boldText} {subText}
    </Alert>
  )
}
