# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class User < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  include CrossShardUser

  canvas_sync_features :defaults

  # include CanvasSync::Concerns::LiveEventSync
  # around_process_live_event do |user, blk|
  #   blk.call
  # rescue Footrest::HttpError::Unauthorized => e
  #   # This can happen when a new user is created, but hasn't setup a login on Canvas yet.
  #   Rails.logger.info("Failed to fetch user #{canvas_user_id}: #{e.backtrace}")
  # end

  validates :canvas_id, uniqueness: true, presence: true
  has_many :pseudonyms, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :enrollments, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :admins, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :admin_roles, through: :admins, source: :role
  has_many :submissions, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :group_memberships, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :rubrics, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_one :user_config, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :course_progresses, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :courses, through: :enrollments

  has_many :calendar_events, primary_key: :canvas_id, foreign_key: :canvas_user_id

  belongs_to :role, foreign_key: :canvas_role_id, primary_key: :canvas_id, optional: true

  K5_GRADE_LEVELS = %w[kg 1 2 3 4 5].freeze

  api_syncable({
                 # sis_id: :sis_user_id, # sis_id is not included in DB Schema
                 email: :email,
                 login_id: :login_id,
                 name: :name,
                 sortable_name: :sortable_name,
                 first_name: :short_name
               }, ->(api) { api.user_detail(canvas_id) })

  scope :active, -> { where(workflow_state: 'active') }
  scope :k5_grade_students, -> { where('lower(users.grade_level) in (?)', K5_GRADE_LEVELS) }

  # HACK: To support Cross Shard updates
  # Returns the canvas_user_id as a string.
  # This method overrides the default getter for canvas_user_id,
  # ensuring that the ID is always returned as a string rather than
  # its native database type (likely an integer).
  def canvas_id
    super.to_s
  end

  def enrolled_courses
    enrollments.not_deleted
               .where("enrollments.base_role_type = 'StudentEnrollment' AND courses.workflow_state<>'deleted'")
               .eager_load(:course)
  end

  def course_date_ranges
    enrollments.students.not_deleted.left_outer_joins(course: [:term])
               .group('enrollments.canvas_user_id')
               .select('MIN(courses.start_at) course_start, MAX(courses.end_at) course_end, MIN(terms.start_at) term_start, MAX(terms.end_at) term_end')
  end

  def k5_student?
    # grade_levels = K5_GRADE_LEVELS.map(&:downcase)
    K5_GRADE_LEVELS.include?(grade_level.to_s.downcase)
  end

  def observee_ids
    enrollments.active.observers.map(&:canvas_associated_user_id).uniq.compact
  end

  # build empty user_config in case of no user_config
  def user_config
    super || build_user_config
  end

  def k5_learning_coach?
    User.where(canvas_id: observee_ids).k5_grade_students.any?
  end

  def fetch_calendar_events
    Rails.logger.info("Fetching events for student canvas_id #{canvas_id}")

    canvas_sync_client
      .get("/api/v1/users/#{canvas_id}/calendar_events", all_events: true, excludes: %w[description child_events assignment])
      .all_pages!
  end

  # Fetches calendar events for the user and their selectable courses from Canvas Realtime API
  # Optionally limits results to a given date range
  # Params:
  # - date_range (Range): optional Ruby date range (e.g., Date.today..1.week.from_now)
  def canvas_calendar_events(date_range = nil)
    # Build context codes: user + all selectable course canvas_ids
    course_canvas_ids = Course.selectable.joins(:enrollments).where(enrollments: { canvas_user_id: canvas_id }).pluck(:canvas_id).uniq
    context_codes = ["user_#{canvas_id}"] + course_canvas_ids.map { |cid| "course_#{cid}" }
    allowed_keys = CalendarEvent.attribute_names.map(&:to_sym)

    params = {
      excludes: %w[description child_events assignment]
    }

    if date_range
      params[:start_date] = date_range.begin.utc.iso8601 if date_range.begin
      params[:end_date]   = date_range.end.utc.iso8601   if date_range.end
    end

    # Fetch and combine paginated results in batches of 10 context codes
    events = context_codes.each_slice(10).flat_map do |codes_batch|
      canvas_sync_client
        .get("/api/v1/users/#{canvas_id}/calendar_events", **params, context_codes: codes_batch)
        .all_pages!.to_a
    end

    # Normalize events: rename :id to :canvas_id, and initialize CalendarEvent objects
    events.map do |event|
      event[:canvas_id] = event.delete(:id)
      CalendarEvent.new(event.slice(*allowed_keys))
    end
  end
end
