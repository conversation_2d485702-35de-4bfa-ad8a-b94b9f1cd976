# frozen_string_literal: true

class CsvProcessor::CourseImporter < CsvProcessor::Base
  def process_row(row, idx)
    return if [row['sourcedId'], row['courseSourcedId']].all?(&:blank?)

    course = Course.find_by(sis_id: row['sourcedId']) if row['sourcedId'].present?
    course ||= Course.find_by(sis_id: row['courseSourcedId']) if row['courseSourcedId'].present?
    course&.update!(grade_level: row['grades'])
  rescue StandardError => e
    rows_with_errors << idx
    Rails.logger.error "Error processing row: #{row.inspect}. Error: #{e.message}. Backtrace: #{e.backtrace}"
  end
end
