# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Submission, type: :model do
  let(:course) { create(:course, canvas_id: 12) }
  let(:assignment) { create(:assignment, canvas_id: 34, canvas_context_id: course.canvas_id, points_possible: 100, due_at: 1.day.from_now) }
  let(:user) { create(:user, canvas_id: 56) }
  let(:submission) do
    create(:submission,
           canvas_id: 78,
           canvas_course_id: course.canvas_id,
           canvas_assignment_id: assignment.canvas_id,
           canvas_user_id: user.canvas_id,
           score: 85.0,
           points_possible: 100.0,
           workflow_state: 'graded')
  end

  describe 'associations' do
    it { should belong_to(:assignment).optional }
    it { should belong_to(:user).optional }
    it { should belong_to(:course).optional }
  end

  describe 'validations' do
    subject { build(:submission) }

    it { should validate_presence_of(:canvas_id) }
    it { should validate_uniqueness_of(:canvas_id) }
  end

  describe '#canvas_user_id' do
    it 'returns canvas_user_id as string' do
      submission.update(canvas_user_id: 123)

      expect(submission.canvas_user_id).to eq('123')
      expect(submission.canvas_user_id).to be_a(String)
    end
  end

  describe '#requirement_status' do
    context 'when submission is mastered' do
      before do
        submission.update(workflow_state: 'graded', score: 85.0, points_possible: 100.0)
      end

      it 'returns mastered' do
        expect(submission.requirement_status).to eq('mastered')
      end
    end

    context 'when submission is not mastered' do
      before do
        submission.update(workflow_state: 'graded', score: 75.0, points_possible: 100.0)
      end

      it 'returns not mastered' do
        expect(submission.requirement_status).to eq('not mastered')
      end
    end

    context 'when submission is not completed and not past due' do
      before do
        submission.update(submitted_at: nil, workflow_state: 'unsubmitted', due_at: nil)
        assignment.update(due_at: 1.day.from_now)
      end

      it 'returns not completed' do
        expect(submission.requirement_status).to eq('not completed')
      end
    end

    context 'when submission is not completed and past due' do
      before do
        submission.update(submitted_at: nil, workflow_state: 'unsubmitted', due_at: nil)
        assignment.update(due_at: 1.day.ago)
      end

      it 'returns past due' do
        expect(submission.requirement_status).to eq('past due')
      end
    end
  end

  describe '#master?' do
    it 'returns true when graded and score >= 80' do
      submission.update(workflow_state: 'graded', score: 85.0, points_possible: 100.0)

      expect(submission.master?).to be true
    end

    it 'returns true when submitted and score >= 80' do
      submission.update(workflow_state: 'submitted', score: 90.0, points_possible: 100.0)

      expect(submission.master?).to be true
    end

    it 'returns false when score < 80' do
      submission.update(workflow_state: 'graded', score: 75.0, points_possible: 100.0)

      expect(submission.master?).to be false
    end

    it 'returns false when not graded or submitted' do
      submission.update(submitted_at: nil, score: 85.0, points_possible: 100.0)

      expect(submission.master?).to be false
    end
  end

  describe '#not_master?' do
    it 'returns true when graded and score < 80' do
      submission.update(workflow_state: 'graded', score: 75.0, points_possible: 100.0)

      expect(submission.not_master?).to be true
    end

    it 'returns false when score >= 80' do
      submission.update(workflow_state: 'graded', score: 85.0, points_possible: 100.0)

      expect(submission.not_master?).to be false
    end

    it 'returns false when not graded or submitted' do
      submission.update(submitted_at: nil, score: 75.0, points_possible: 100.0)

      expect(submission.not_master?).to be false
    end
  end

  describe '#not_completed?' do
    it 'returns true when not graded/submitted and not past due' do
      submission.update(submitted_at: nil)
      assignment.update(due_at: 1.day.from_now)

      expect(submission.not_completed?).to be true
    end

    it 'returns false when graded' do
      submission.update(workflow_state: 'graded')
      assignment.update(due_at: 1.day.from_now)

      expect(submission.not_completed?).to be false
    end

    it 'returns false when past due' do
      submission.update(submitted_at: nil, due_at: nil)
      assignment.update(due_at: 1.day.ago)

      expect(submission.not_completed?).to be false
    end
  end

  describe '#not_completed_past_due?' do
    it 'returns true when not graded/submitted and past due' do
      submission.update(submitted_at: nil, due_at: nil)
      assignment.update(due_at: 1.day.ago)

      expect(submission.not_completed_past_due?).to be true
    end

    it 'returns false when graded' do
      submission.update(workflow_state: 'graded')
      assignment.update(due_at: 1.day.ago)

      expect(submission.not_completed_past_due?).to be false
    end

    it 'returns false when not past due' do
      submission.update(submitted_at: nil)
      assignment.update(due_at: 1.day.from_now)

      expect(submission.not_completed_past_due?).to be false
    end
  end

  describe '#calculated_score' do
    it 'calculates percentage score correctly' do
      submission.update(score: 85.0, points_possible: 100.0)

      expect(submission.calculated_score).to eq(85.0)
    end

    it 'returns 0.0 when points_possible is 0' do
      submission.update(score: 85.0, points_possible: 0.0)

      expect(submission.calculated_score).to eq(0.0)
    end

    it 'returns 0.0 when points_possible is nil' do
      submission.update(score: 85.0, points_possible: nil)
      assignment.update(points_possible: nil)

      expect(submission.calculated_score).to eq(0.0)
    end
  end

  describe '#points_possible' do
    it 'returns submission points_possible when set' do
      submission.update(points_possible: 90.0)

      expect(submission.points_possible).to eq(90.0)
    end

    it 'falls back to assignment points_possible when submission points_possible is nil' do
      submission.update(points_possible: nil)
      assignment.update(points_possible: 100.0)

      expect(submission.points_possible).to eq(100.0)
    end
  end

  describe '#date_passed?' do
    context 'when submission has due_at' do
      it 'returns true when due_at is in the past' do
        submission.update(due_at: 1.day.ago)

        expect(submission.date_passed?).to be true
      end

      it 'returns false when due_at is in the future' do
        submission.update(due_at: 1.day.from_now)

        expect(submission.date_passed?).to be false
      end
    end

    context 'when submission has no due_at' do
      before { submission.update(due_at: nil) }

      it 'returns true when assignment due_at is in the past' do
        assignment.update(due_at: 1.day.ago)

        expect(submission.date_passed?).to be true
      end

      it 'returns false when assignment due_at is in the future' do
        assignment.update(due_at: 1.day.from_now)

        expect(submission.date_passed?).to be false
      end

      it 'returns false when no due date is set' do
        assignment.update(due_at: nil)

        expect(submission.date_passed?).to be false
      end
    end
  end
end
