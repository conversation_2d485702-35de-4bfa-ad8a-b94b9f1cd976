# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateContextModules < ActiveRecord::Migration[5.1]
  def change
    create_table :context_modules do |t|
      t.bigint :canvas_id
      t.bigint :canvas_context_id
      t.string :canvas_context_type
      t.integer :position
      t.string :name
      t.string :workflow_state
      t.datetime :deleted_at
      t.datetime :unlock_at
      t.jsonb :prerequisites
      t.jsonb :completion_requirements

      t.timestamps
    end

    add_index :context_modules, :canvas_id, unique: true
    add_index :context_modules, [:canvas_context_id, :canvas_context_type], name: "index_context_modules_on_context"
  end
end
