class CreateCourseProgresses < ActiveRecord::Migration[7.0]
  def change
    create_table :course_progresses do |t|
      t.bigint :canvas_course_id, null: false
      t.bigint :canvas_user_id, null: false
      t.integer :requirement_count
      t.integer :requirement_completed_count
      t.datetime :completion_date

      t.timestamps
    end

    add_index :course_progresses, :canvas_user_id
    add_index :course_progresses, :canvas_course_id
    add_index :course_progresses, [:canvas_user_id, :canvas_course_id], unique: true
  end
end
