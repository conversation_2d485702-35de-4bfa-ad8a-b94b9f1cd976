# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::ExternalResourcesController, type: :controller do
  render_views
  let(:organization) { current_organization }
  let(:canvas_user) { create(:user) }
  let!(:external_resource) { create(:external_resource, description: 'Dummy description', base_url: 'http://example.com') }
  let(:session) do
    PandaPal::Session.create(panda_pal_organization_id: organization.id,
                             data: {
                               canvas_user_id: canvas_user.canvas_id,
                               organization_key: organization.key
                             })
  end

  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }

  describe 'GET /index' do
    it 'should return external_resources data as JSON' do
      get :index, params: default_params, format: :json
      data = JSON.parse(response.body)
      expect(response.status).to eq 200
      expect(data).not_to be_empty
      external_resources = data['external_resources']
      expect(external_resources).not_to be_empty
      expect(external_resources[0]['base_url']).not_to be_blank
      expect(external_resources[0]['position']).not_to be_blank
    end
  end

  describe 'PUT /update' do
    it 'should update external_resource' do
      expect(external_resource.description).not_to eq('Updated description')
      expect(external_resource.base_url).not_to eq('http://instructure-example.com')

      put :update, params: default_params.merge({ upload: { description: 'Updated description', base_url: 'http://instructure-example.com' } }, id: external_resource.id), format: :json
      data = JSON.parse(response.body)
      expect(response.status).to eq 200
      expect(data).not_to be_empty
      expect(external_resource.reload.description).to eq('Updated description')
      expect(external_resource.base_url).to eq('http://instructure-example.com')
    end
  end

  describe 'DELETE /destroy' do
    it 'should destroy external_resource' do
      delete :destroy, params: default_params.merge(id: external_resource.id), format: :json
      expect(response.status).to eq 204
    end
  end
end
