# frozen_string_literal: true

module LiveEvents
  class ModuleItemUpdatedEvent < LiveEvents::ModuleItemEvent
    def process
      # Ensure context module exists and is synced first
      context_module = ContextModule.find_or_initialize_by(
        canvas_id: local_canvas_id(payload[:module_id]),
        canvas_context_id: local_canvas_id(payload[:context_id]),
        canvas_context_type: payload[:context_type]
      )
      context_module.sync_from_api

      Rails.logger.info("#{event_info} Context module synced: module_id=#{context_module.canvas_id}, " \
                        "context_id=#{context_module.canvas_context_id}")

      context_module_item = ContextModuleItem.find_or_initialize_by(
        canvas_id: local_canvas_id(payload[:module_item_id]),
        canvas_context_module_id: local_canvas_id(payload[:module_id])
      )
      # TODO: Sync Workflow state. It is missing from the API response
      context_module_item.sync_from_api

      Rails.logger.info("#{event_info} Module item updated and synced: module_item_id=#{context_module_item.canvas_id}, " \
                        "module_id=#{payload[:module_id]}")
    end

    private

    def event_info
      @event_info ||= "[Org: #{current_organization.name}] [ModuleItemUpdatedEvent]"
    end
  end
end
