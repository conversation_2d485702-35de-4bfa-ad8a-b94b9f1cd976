# frozen_string_literal: true

class SyncGradingSchemeJob < CanvasSync::Job
  queue_as :critical

  def perform(_options = {})
    courses = Course.where.not(grading_standard_id: nil)
                    .pluck(:canvas_id, :grading_standard_id)

    courses.each do |course_id, grading_standard_id|
      grading_scheme = if grading_standard_id.zero?
                         # Zero is the default canvas grading scheme if they don't create their own.
                         { 'grading_scheme' => default_standard_grading_scheme.map { |scheme| scheme.slice('name', 'value') } }
                       else
                         canvas_sync_client.course_grading_standard(course_id, grading_standard_id)
                       end
      update_grading_scheme(course_id, grading_scheme['grading_scheme']) unless grading_scheme.nil?
    rescue Footrest::HttpError::NotFound => e
      Rails.logger.info(e.message)
      # try the account grading scheme if we can't find one at the course level
      account_grading_standard(course_id, grading_standard_id)
    end
  end

  def account_grading_standard(course_id, grading_standard_id)
    grading_scheme = canvas_sync_client.account_grading_standard(current_organization.canvas_account_id, grading_standard_id)
    update_grading_scheme(course_id, grading_scheme['grading_scheme'])
  rescue Footrest::HttpError::NotFound => e
    Rails.logger.info(e.message)
  end

  def update_grading_scheme(course_id, grading_scheme)
    gs = GradingScheme.find_or_initialize_by(canvas_course_id: course_id)
    gs.data = grading_scheme.to_a
    gs.scheme_color_type = gs.data.size > 2 ? GradingScheme.scheme_color_types[:standard] : GradingScheme.scheme_color_types[:mastery]

    gs.save! if gs.changed?
  end
end
