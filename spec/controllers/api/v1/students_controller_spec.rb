# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::StudentsController, type: :controller do
  render_views
  let(:organization) { current_organization }
  let!(:course) { create(:course) }
  let(:student) { create(:user) }
  let(:canvas_user) { create(:user) }
  let!(:enrollment) { create(:enrollment, canvas_course_id: course.canvas_id, base_role_type: 'StudentEnrollment', canvas_user_id: student.canvas_id) }
  let(:session) do
    PandaPal::Session.create(panda_pal_organization_id: organization.id,
                             data: {
                               canvas_user_id: student.canvas_id,
                               organization_key: organization.key
                             })
  end

  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }
end
