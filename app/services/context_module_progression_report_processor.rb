# frozen_string_literal: true

class ContextModuleProgressionReportProcessor < CanvasSync::Processors::ReportProcessor
  DEFAULT_BATCH_SIZE = 10_000
  MAPPING = {
    report_columns: {
      canvas_course_id: {
        report_column: :course_id,
        type: :integer
      },
      canvas_module_id: {
        report_column: :context_module_id,
        type: :integer
      },
      canvas_module_item_id: {
        report_column: :context_module_item_id,
        type: :integer
      },
      canvas_user_id: {
        report_column: :user_id,
        type: :integer
      },
      canvas_content_type: {
        report_column: :content_type,
        type: :string
      },
      canvas_content_id: {
        report_column: :content_id,
        type: :integer
      },
      canvas_content_title: {
        report_column: :content_title,
        type: :string
      },
      canvas_page_url: {
        report_column: :page_url,
        type: :string
      },
      requirement_type: {
        report_column: :requirement_type,
        type: :string
      },
      requirement_status: {
        report_column: :requirement_status,
        type: :string
      },
      lock_at: {
        report_column: :lock_at,
        type: :datetime
      },
      todo_date: {
        report_column: :todo_date,
        type: :datetime
      },
      module_progress_status: {
        report_column: :module_progress_status,
        type: :string
      }
    }
  }.with_indifferent_access.freeze

  def self.process(report_file_path, _options, _report_id)
    new(report_file_path)
  end

  # rubocop:disable Lint/MissingSuper
  def initialize(report_file_path)
    import(report_file_path)
  end
  # rubocop:enable Lint/MissingSuper

  private

  def import(report_file_path)
    database_column_names = MAPPING[:report_columns].keys

    update_conditions = {
      columns: database_column_names,
      condition: condition_sql(ContextModuleProgression, database_column_names),
      conflict_target: [:canvas_module_id, :canvas_module_item_id, :canvas_user_id],
      index_name: :index_context_modules_progress_on_composit_columns
    }
    options = { validate: false, on_duplicate_key_update: update_conditions }

    rows = []
    CSV.foreach(report_file_path, headers: true, header_converters: :symbol) do |row|
      next if row.nil?

      rows << MAPPING[:report_columns].map do |_db_col, csv_column|
        value = nil
        value = row[csv_column[:report_column]] if csv_column[:report_column]
        value = YAML.load(value) if csv_column[:type].to_sym == :jsonb
        value
      end

      if rows.length >= batch_size
        ContextModuleProgression.import(database_column_names, rows, options)
        rows = []
      end
    end

    ContextModuleProgression.import(database_column_names, rows, options)
  end

  def batch_size
    batch_size = ENV['BULK_IMPORTER_BATCH_SIZE'].to_i
    batch_size.positive? ? batch_size : DEFAULT_BATCH_SIZE
  end

  def condition_sql(klass, columns)
    columns_str = columns.map { |c| "#{klass.quoted_table_name}.#{c}" }.join(', ')
    excluded_str = columns.map { |c| "EXCLUDED.#{c}" }.join(', ')
    "(#{columns_str}) IS DISTINCT FROM (#{excluded_str})"
  end
end
