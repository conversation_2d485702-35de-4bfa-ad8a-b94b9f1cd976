# frozen_string_literal: true

module LiveEvents
  class CourseGradeChangeEvent < LiveEvents::GradeEvent
    def process
      return if course.nil? || course.grading_standard_id.nil? || course.grading_scheme.nil? || enrollments.blank?

      enrollments.each do |enrollment|
        score = enrollment.score || enrollment.build_score(canvas_enrollment_id: enrollment.canvas_id)
        score.assign_attributes(score_attributes)

        # Handle current_letter_grade, unposted_letter_grade, final_letter_grade
        score.current_letter_grade = course.grading_scheme.score_to_grade(score.current_score)&.dig('name')
        score.unposted_letter_grade = course.grading_scheme.score_to_grade(score.unposted_current_score)&.dig('name')
        score.final_letter_grade = course.grading_scheme.score_to_grade(score.final_score)&.dig('name')

        score.save
      end
    end

    private

    def score_attributes
      payload.slice(:current_score, :unposted_current_score, :final_score, :workflow_state)
    end

    def course
      @course ||= Course.find_by(canvas_id: local_canvas_id(payload[:course_id]))
    end

    def enrollments
      @enrollments ||= course.enrollments.not_deleted.students.where(canvas_user_id: payload[:user_id], canvas_course_id: payload[:course_id])
    end

    # Live events will use a canvas global ID (cross shard) for any ID's provided. This method will return the local ID.
    def local_canvas_id(id)
      id.to_i % 10_000_000_000_000
    end
  end
end
