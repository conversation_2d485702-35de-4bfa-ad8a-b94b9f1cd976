# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2025-05-26 08:56:26 UTC using RuboCop version 1.68.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Max, AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, AllowedPatterns.
# URISchemes: http, https

# Offense count: 1
Lint/ToEnumArguments:
  Exclude:
    - 'app/models/concerns/cross_shard_user.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, AllowUnusedKeywordArguments, IgnoreEmptyMethods, IgnoreNotImplementedMethods.
Lint/UnusedMethodArgument:
  Exclude:
    - 'app/jobs/refresh_cross_shard_users_job.rb'

# Offense count: 1
# Configuration parameters: CountComments, Max, CountAsOne.
Metrics/ClassLength:
  Exclude:
    - 'app/services/query_builder/student_course_progress.rb'

# Offense count: 1
# Configuration parameters: CountComments, Max, CountAsOne, AllowedMethods, AllowedPatterns.
Metrics/MethodLength:
  Exclude:
    - 'app/models/concerns/cross_shard_user.rb'

# Offense count: 1
# Configuration parameters: AllowedMethods, AllowedPatterns, Max.
Metrics/PerceivedComplexity:
  Exclude:
    - 'app/models/concerns/cross_shard_user.rb'
