# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::AccessController, type: :controller do
  let(:organization) { current_organization }
  let!(:course) { create(:course) }
  let(:student) { create(:user) }
  let(:canvas_user) { create(:user) }
  let!(:enrollment) { create(:enrollment, canvas_course_id: course.canvas_id, base_role_type: 'StudentEnrollment', canvas_user_id: student.canvas_id) }

  let(:default_params) { { organization_id: organization.id } }

  describe 'GET /check' do
    it 'should return access data as JSON' do
      get :check, params: default_params.merge(id: student.canvas_id), format: :json

      data = JSON.parse(response.body)
      expect(response.status).to eq 200
      expect(data).not_to be_empty
      expect(data['canvas_id']).to eq(student.canvas_id.to_s)
      expect(data['is_k5_student']).to eq(false)
      expect(data['is_learning_coach']).to eq(false)
    end
  end
end
