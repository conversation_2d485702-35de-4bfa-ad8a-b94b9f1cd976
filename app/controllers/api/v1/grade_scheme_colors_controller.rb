# frozen_string_literal: true

class Api::V1::GradeSchemeColorsController < ApplicationController
  before_action :set_account
  before_action :set_scheme, only: [:show, :update]

  def index
    authorize! :read, AccountGradingSchemeColor

    @account_grading_schemes = @account.account_grading_scheme_colors.select(:id, :canvas_account_id, :scheme_color_type, :name, :range_value, :color_code, :default_color_code)
                                       .order(:id)
                                       .group_by(&:scheme_color_type)
  end

  def show
    authorize! :read, @grading_scheme
  end

  def update
    authorize! :update, @grading_scheme

    if @grading_scheme.update(grading_scheme_params)
      render :show, status: :ok
    else
      render json: { errors: @grading_scheme.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def grading_scheme_params
    params.require(:grading_scheme).permit(:color_code)
  end

  def set_scheme
    @grading_scheme = @account.account_grading_scheme_colors.find_by(id: params[:id])
  end

  def set_account
    @account = Account.find_by(canvas_id: params[:account_id])
  end
end
