# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateContextModuleItems < ActiveRecord::Migration[5.1]
  def change
    create_table :context_module_items do |t|
      t.bigint :canvas_id
      t.bigint :canvas_context_module_id
      t.integer :position
      t.bigint :canvas_content_id
      t.string :canvas_content_type
      t.bigint :canvas_assignment_id
      t.string :workflow_state

      t.timestamps
    end

    add_index :context_module_items, :canvas_id, unique: true
    add_index :context_module_items, :canvas_context_module_id
  end
end
