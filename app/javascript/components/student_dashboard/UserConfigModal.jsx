import { useEffect, useState } from 'react';

import { Modal } from '@instructure/ui-modal';
import { View } from '@instructure/ui-view';
import { <PERSON><PERSON>, CloseButton } from '@instructure/ui-buttons';
import { Heading } from "@instructure/ui-heading";
import { Flex } from '@instructure/ui-flex';
import { RadioInput, RadioInputGroup } from '@instructure/ui-radio-input';
import { Text } from '@instructure/ui-text';

import moment from 'moment-timezone';

import InstSelect from '../../shared/components/InstSelect';
import GroupSelect from '../../shared/components/GroupSelect';
import ThemeIcon from '../../shared/components/ThemeIcon';
import * as API from "../../utils/api";
import * as GlobalConstant from '../../utils/constants';

const UserConfigModal = (props) => {
  const { isModalOpen, toggleModal, userId, onSave } = props;
  const [hoveredTheme, setHoveredTheme] = useState(null);

  const [state, setState] = useState({
    confId: null,
    language: "",
    timezone: "",
    homepage: "",
    theme: "no_theme",
    audioEnabled: true,
  });

  useEffect(() => {
    if (userId) {
      getUserConfig();
    }
  }, []);

  const getUserConfig = () => {
    const canvas_user_locale = window.ENV.canvas_user_locale;
    const canvas_user_timezone = window.ENV.canvas_user_timezone;

    API.getUserDashboardConfig(userId)
      .then((response) => response.data)
      .then((response) => {
        const userConfigs = {
          confId: response.id,
          homepage: response.home_page,
          language: response.language || canvas_user_locale,
          timezone: response.timezone || canvas_user_timezone,
          theme: response.theme || "no_theme",
          audioEnabled: response.audio_enabled !== undefined ? response.audio_enabled : true,
        };
        setState(userConfigs);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const saveUserConfigs = () => {
    const userConfigs = {
      home_page: state.homepage,
      language: state.language,
      timezone: state.timezone,
      theme: state.theme,
      audio_enabled: state.audioEnabled
    };

    if(state.confId !== null) {
      API.updateUserDashboardConfig(userId, state.confId, userConfigs)
        .then((response) => response.data)
        .then((response) => {
          // Update window.ENV.user_config with the latest data
          Object.assign(window.ENV.user_config, response);
          toggleModal();
          onSave('success', response);
        })
        .catch((error) => {
          onSave('error', null);
          console.log(error);
        });
    } else {
      API.createUserDashboardConfig(userId, userConfigs)
        .then((response) => response.data)
        .then((response) => {
          setState((prevState) => ({
            ...prevState,
            confId: response.id,
          }))
          // Update window.ENV.user_config with the new data
          if (window.ENV) {
            window.ENV.user_config = response;
          }
          toggleModal();
          onSave('success', response);
        })
        .catch((error) => {
          onSave('error', null);
          console.log(error);
        });
    }
  };

  const closeModal = () => {
    toggleModal()
  };

  const renderLanguages = () => {
    return(
      <View>
        <InstSelect
          label="Languages"
          selectedOptionId={state.language}
          options={GlobalConstant.CANVAS_LOCALES}
          inputValue={state.language || ''}
          onChange={(lang) =>
            setState((prevState) => ({
              ...prevState,
              language: lang,
            }))
          }
        />
      </View>
    )
  }

  const renderTimeZones = () => {
    return(
      <View>
        <GroupSelect
          label="Time Zone"
          options={GlobalConstant.TIME_ZONES}
          selectedOptionId={state.timezone}
          inputValue={state.timezone || ''}
          onChange={(tz) =>
            setState((prevState) => ({
              ...prevState,
              timezone: tz,
            }))
          }
        />
      </View>
    )
  }

  const renderHomePages = () => {
    return (
      <RadioInputGroup
        layout="columns"
        name="homepage"
        defaultValue={state.homepage}
        description="Default Dashboard Homepage"
        onChange={(event) =>
          setState((prevState) => ({
            ...prevState,
            homepage: event.target.value,
          }))
        }
      >
        {GlobalConstant.HOME_PAGES.map(input =>
            <RadioInput
              key={input.value}
              value={input.value}
              label={input.label}
            />
        )}
      </RadioInputGroup>
    )
  }

  const renderThemeSelection = () => {
    const themeOptions = [
      { value: 'forest', label: 'Forest' },
      { value: 'winter', label: 'Winter' },
      { value: 'space', label: 'Space' },
      { value: 'no_theme', label: 'None' }
    ];

    const handleThemeClick = (themeValue) => {
      setState((prevState) => ({
        ...prevState,
        theme: themeValue,
      }));
    };

    return (
      <View id='theme-selection'>
        <Text size="medium" weight="bold" margin="0 0 small 0">
          Dashboard Visual Theme
        </Text>
        <Flex direction="row" margin="small none none none">
          {themeOptions.map(option => {
            const isSelected = state.theme === option.value;
            const isHovered = hoveredTheme === option.value;
            const overlay = !isSelected && !isHovered
              ? {
                  color: 'white',
                  blend: 'normal',
                  opacity: '8',
                }
              : {};

            return (
              <Flex.Item key={option.value}>
                <Flex direction="column" alignItems="center">
                  <Flex.Item>
                    <View
                      as="button"
                      onClick={() => handleThemeClick(option.value)}
                      onMouseEnter={() => setHoveredTheme(option.value)}
                      onMouseLeave={() => setHoveredTheme(null)}
                      cursor="pointer"
                      background="transparent"
                      borderWidth='none'
                      themeOverride={{focusOutlineStyle: 'none'}}
                      className={isSelected && 'selected-icon-border'}
                    >
                      <ThemeIcon
                        theme={option.value}
                        size="medium"
                        overlay={overlay}
                      />
                    </View>
                  </Flex.Item>
                  <Flex.Item>
                    <Text size="small" weight="normal">
                      {option.label}
                    </Text>
                  </Flex.Item>
                </Flex>
              </Flex.Item>
            );
          })}
        </Flex>
      </View>
    )
  }

  const renderAudioSettings = () => {
    return (
      <RadioInputGroup
        layout="columns"
        name="audioEnabled"
        defaultValue={state.audioEnabled ? "enabled" : "disabled"}
        description="Dashboard Audio"
        onChange={(event) =>
          setState((prevState) => ({
            ...prevState,
            audioEnabled: event.target.value === "enabled",
          }))
        }
      >
        <RadioInput
          value="enabled"
          label="Audio On"
        />
        <RadioInput
          value="disabled"
          label="Audio Off"
        />
      </RadioInputGroup>
    )
  }

  return (
    <View>
      <Modal
        open={isModalOpen}
        onDismiss={closeModal}
        size="medium"
        label="Dashboard Settings"
        shouldCloseOnDocumentClick
      >
        <Modal.Header>
          <CloseButton
            placement="end"
            offset="small"
            onClick={closeModal}
            screenReaderLabel="Close"
          />
          <Heading level="h2">Dashboard Settings</Heading>
        </Modal.Header>
        <Modal.Body>
          <View>
            <Flex direction="column">
              <Flex.Item padding="small xx-small">
                { renderLanguages() }
              </Flex.Item>
              <Flex.Item padding="small xx-small">
                { renderTimeZones() }
              </Flex.Item>
              <Flex.Item padding="small xx-small">
                { renderHomePages() }
              </Flex.Item>
              <Flex.Item padding="small xx-small">
                { renderThemeSelection() }
              </Flex.Item>
              <Flex.Item padding="small xx-small">
                { renderAudioSettings() }
              </Flex.Item>
            </Flex>
          </View>
        </Modal.Body>
        <Modal.Footer>
          <Button
            margin="0 x-small 0 0"
            onClick={closeModal}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={saveUserConfigs}
          >
            Save
          </Button>
        </Modal.Footer>
      </Modal>
    </View>
  )
}

export default UserConfigModal
