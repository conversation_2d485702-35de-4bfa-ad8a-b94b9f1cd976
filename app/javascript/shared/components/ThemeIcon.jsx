import { View } from '@instructure/ui-view';
import { Img } from '@instructure/ui-img';

// Import theme icons
import forestIcon from '../../assets/themes/icons/forest.jpg';
import winterIcon from '../../assets/themes/icons/winter.jpg';
import spaceIcon from '../../assets/themes/icons/space.jpg';
import noThemeIcon from '../../assets/themes/icons/no-theme.jpg';

const ThemeIcon = ({ theme, size = 'medium', overlay = { opacity: '0' } }) => {
  const getIconSrc = () => {
    switch (theme) {
      case 'forest':
        return forestIcon;
      case 'winter':
        return winterIcon;
      case 'space':
        return spaceIcon;
      case 'no_theme':
        return noThemeIcon;
      default:
        return noThemeIcon;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return { width: '40px', height: '40px' };
      case 'medium':
        return { width: '60px', height: '60px' };
      case 'large':
        return { width: '80px', height: '80px' };
      default:
        return { width: '60px', height: '60px' };
    }
  };

  const iconSize = getIconSize();

  return (
    <View
      as="div"
      width={iconSize.width}
      height={iconSize.height}
      borderWidth="small"
      borderRadius='large'
      display="block"
      overflowX='hidden'
      overflowY='hidden'
      position="relative"
    >
      <Img
        src={getIconSrc()}
        alt={`${theme} theme icon`}
        overlay={overlay}
        width={iconSize.width}
        height={iconSize.height}
      />
    </View>
  );
};

export default ThemeIcon;
