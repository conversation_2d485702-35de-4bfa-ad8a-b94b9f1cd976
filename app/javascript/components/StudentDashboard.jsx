import '@/application.css'
import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { IconUserLine,
         IconSettingsLine } from '@instructure/ui-icons';
import { Link } from '@instructure/ui-link';
import { Alert } from '@instructure/ui-alerts';

import * as API from "../utils/api";
import UserConfigModal from './student_dashboard/UserConfigModal';
import DashboardBody from './student_dashboard/DashboardBody';
import CourseProgress from './courses/CourseProgress';
import ThemeWrapper from '../shared/components/ThemeWrapper';
import { ThemeProvider, useTheme } from '../shared/contexts/ThemeContext';
import { Button } from '@instructure/ui-buttons';


const StudentDashboardContent = () => {
  const { updateTheme } = useTheme();
  const { currentTheme, themeAssets } = useTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    type: '',
    message: ''
  });
  const [currentView, setCurrentView] = useState('');


  const toggleModal = (e) => {
    if (!isModalOpen) {
      setShowAlert(false);
    }
    setIsModalOpen(!isModalOpen);
  };

  const userInfo = {
    name: window.ENV.user.sortable_name,
    canvasId: window.ENV.user.canvas_id
  }

  const afterConfigSave = (resType, updatedConfig) => {
    if (resType === 'success' && updatedConfig?.theme) {
      // Update theme smoothly without page reload
      updateTheme(updatedConfig.theme);
    }

    setAlertConfig({
      type: resType === 'error' ? 'warning' : 'info',
      message: (
        <>
          <Text weight='bold'>{resType === 'error' ? 'Error:' : 'Saved:'}</Text>
          {resType === 'error'
            ? ' The Dashboard Settings could not be saved.'
            : ' The Dashboard Settings have been Saved.'}
        </>
      ),
    });
    setShowAlert(true);
  };

  const renderAlert = () => {
    return (
      <View as="div" padding="0 large">
        <Alert
          variant={alertConfig.type}
          renderCloseButtonLabel="Close"
          margin="none large"
          timeout={5000}
        >
          {alertConfig.message}
        </Alert>
      </View>
    )
  }

  const renderStudentDashboard = () => {
    return <DashboardBody
              userId={userInfo.canvasId}
              isK5Student={true}
              setCurrentView={setCurrentView}
              currentTheme={currentTheme}
              themeAssets={themeAssets}
            />
  }

  const activePageName = (() => {
    switch (currentView) {
      case 'course_view': return 'course';
      case 'resources': return 'resource';
      case 'announcements': return 'announcement';
      case 'agenda_view': return 'Agenda';
      case 'teachers': return 'teacher';
      default: return 'none';
    }
  })();

  const renderCourseProgress = () => {
    return (
      <View
        as='div'
        padding='0 medium 0 large'
        height='100%'
        width='100%'
      >
        <CourseProgress studentId={userInfo.canvasId} courseId={window.ENV.canvas_course_id}/>
      </View>
    )
  }

  const renderPageBody = () => {
    if (window.ENV.context === 'course') {
      return renderCourseProgress();
    }else {
      return renderStudentDashboard();
    }
  }

  return (
    <ThemeWrapper activePage={activePageName}>
      <View as="div" className="dashboard-body-container">
        <View as="div" textAlign="end" className="header-text">
          <Flex margin="none none none">
            <Flex.Item shouldShrink shouldGrow></Flex.Item>
            <Flex.Item padding="x-small"><Text weight="bold"><IconUserLine /> {userInfo.name}</Text></Flex.Item>
            <Flex.Item padding="x-small">|</Flex.Item>
            <Flex.Item padding="x-small">
              <Button
                renderIcon={<IconSettingsLine />}
                color="primary"
                size='small'
                onClick={toggleModal}
                themeOverride={{primaryBackground: '#1E5EFF'}}
                shadaow="above"
                >Settings</Button>
            </Flex.Item>
          </Flex>
        </View>
        { showAlert && renderAlert() }
        { renderPageBody() }
        <UserConfigModal
          key={`userConfigModal-1`}
          isModalOpen={isModalOpen}
          toggleModal={toggleModal}
          userId={userInfo.canvasId}
          onSave={afterConfigSave}
        />
      </View>
    </ThemeWrapper>
  )
}

const StudentDashboard = () => {
  return (
    <ThemeProvider>
      <StudentDashboardContent />
    </ThemeProvider>
  )
}

export default StudentDashboard
