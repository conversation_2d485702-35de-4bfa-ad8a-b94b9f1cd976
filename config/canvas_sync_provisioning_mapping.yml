enrollments:
  conflict_target: canvas_enrollment_id
  report_columns:
    canvas_enrollment_id:
      database_column_name: canvas_id
      type: integer
    canvas_course_id:
      database_column_name: canvas_course_id
      type: integer
    course_id:
      database_column_name: course_sis_id
      type: string
    canvas_user_id:
      database_column_name: canvas_user_id
      type: integer
    user_id:
      database_column_name: user_sis_id
      type: string
    role:
      database_column_name: role
      type: string
    role_id:
      database_column_name: canvas_role_id
      type: integer
    canvas_section_id:
      database_column_name: canvas_section_id
      type: integer
    status:
      database_column_name: workflow_state
      type: string
    base_role_type:
      database_column_name: base_role_type
      type: string
    canvas_associated_user_id:
      database_column_name: canvas_associated_user_id
      type: integer
    created_at:
      database_column_name: canvas_created_at
      type: datetime
    start_at:
      database_column_name: canvas_start_at
      type: datetime
