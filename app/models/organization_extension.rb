# frozen_string_literal: true

module OrganizationExtension
  extend ActiveSupport::Concern
  SHARD_OFFSET = 10_000_000_000_000

  included do
    scheduled_task '0 */3 * * *', :sync_canvas, worker: CanvasSyncStarterWorker

    before_create do
      self.shard ||= Apartment.shard_configurations.keys.sample || 'default' unless Rails.env.test?
    end

    # Runs every 4 hours
    # TODO: It is legacy importer designed to import csv files from SFTP server
    scheduled_task '0 */4 * * *', :sync_sftp_resources_csv, worker: SyncResourcesJob
    scheduled_task '0 */4 * * *', :sync_sftp_resources, worker: SyncData<PERSON>romSftpJob

    # Runs everyday from 11 AM to 7 PM every hour
    scheduled_task '0 11-23 * * *', :hosted_data_push, worker: HostedDataPushJob
  end

  class_methods do
    def for_canvas_shard(cid)
      # HACK: Explicitly convert to integer because we overriding canvas_id method in User model
      cid = cid.to_i
      cid = (cid / SHARD_OFFSET).floor if cid > SHARD_OFFSET
      find_by(canvas_shard_id: cid)
    end
  end

  # Helper method to get base url
  def base_url
    settings.dig(:canvas, :base_url)
  end
end
