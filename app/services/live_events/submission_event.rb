# frozen_string_literal: true

# #
# AUTO GENERATED LIVE EVENT
# This was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#
# LiveEvent message formats can be found at https://canvas.instructure.com/doc/api/file.live_events.html
# In the general case, LiveEvent message content should not be trusted - it is highly possible that events may
# arrive out of order. Most of the CanvasSync LiveEvent templates solve this by always fetching the object from the API.
#

module LiveEvents
  class SubmissionEvent < CanvasSync::LiveEvents::BaseHandler
    def process
      return if Assignment.find_by(canvas_id: local_canvas_id(payload['assignment_id'])).nil?

      submission = Submission.where(canvas_id: local_canvas_id(payload['submission_id'])).first_or_initialize
      submission.canvas_assignment_id = local_canvas_id(payload['assignment_id'])
      submission.canvas_user_id = local_canvas_id(payload['user_id'])
      submission.canvas_course_id = submission.assignment.context.canvas_id
      submission.sync_from_api
    end

    private

    # Live events will use a canvas global ID (cross shard) for any ID's provided. This method will return the local ID.
    def local_canvas_id(id)
      id.to_i % 10_000_000_000_000
    end
  end
end
