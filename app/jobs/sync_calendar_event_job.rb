# frozen_string_literal: true

class SyncCalendarEventJob < CanvasSync::Job
  queue_as :default

  BATCH_SIZE = 100

  # Limited to 10 context codes per request as per Canvas API documentation:
  # https://canvas.instructure.com/doc/api/calendar_events.html#method.calendar_events_api.index
  CONTEXT_BATCH_SIZE = 10

  def perform(_options = {})
    students.in_batches(of: BATCH_SIZE) do |batch|
      batch.each { |student| sync_student_events(student) }
    end
  end

  private

  def students
    User
      .active
      .k5_grade_students
      .joins(:enrollments)
      .where(enrollments: { base_role_type: 'StudentEnrollment' })
      .distinct
  end

  def sync_student_events(student)
    event_ids = []

    student.fetch_calendar_events.each do |event_data|
      event_ids << event_data['id']
      sync_calendar_event(student, event_data)
    rescue StandardError => e
      Rails.logger.error("Failed to sync calendar events for student #{student.id}. Error: #{e.message}")
    end

    remove_stale_events(student, event_ids)
  end

  def sync_calendar_event(student, data)
    event = CalendarEvent.find_or_initialize_by(
      canvas_id: data['id'],
      canvas_user_id: student.canvas_id
    )

    event.assign_attributes(
      user: student,
      title: data['title'],
      start_at: data['start_at'],
      end_at: data['end_at'],
      html_url: data['html_url']
    )

    event.save! if event.changed?
  end

  def remove_stale_events(student, current_event_ids)
    CalendarEvent
      .where(canvas_user_id: student.canvas_id)
      .where.not(canvas_id: current_event_ids)
      .delete_all
  end
end
