class ChangeCanvasUserIdToBigintInTables < ActiveRecord::Migration[7.0]
  def change
    reversible do |dir|
      dir.up do
        change_column :user_shard_associations, :canvas_user_id, :bigint
        change_column :roles, :canvas_id, :bigint
        change_column :enrollments, :canvas_role_id, :bigint
      end

      dir.down do
        change_column :user_shard_associations, :canvas_user_id, :integer
        change_column :roles, :canvas_id, :integer
        change_column :enrollments, :canvas_role_id, :integer
      end
    end
  end
end
