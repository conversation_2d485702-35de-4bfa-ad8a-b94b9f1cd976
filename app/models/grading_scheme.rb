# frozen_string_literal: true

class GradingScheme < ApplicationRecord
  belongs_to :course, primary_key: :canvas_id, foreign_key: :canvas_course_id

  serialize :data
  enum scheme_color_type: { standard: 0, mastery: 1 }

  validates :canvas_course_id, presence: true

  def score_to_grade(score)
    score = [0, BigDecimal(score.to_s)].max # Ensure non-negative score

    data.max_by do |el|
      value = BigDecimal(el['value'].to_s) * 100
      score >= value ? value : -value
    end
  end

  def grading_scheme_colors
    @grading_scheme_colors ||= course.account.account_grading_scheme_colors.where(scheme_color_type: scheme_color_type).order(:id)
  end
end
