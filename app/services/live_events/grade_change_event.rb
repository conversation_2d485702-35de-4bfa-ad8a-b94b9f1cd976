# frozen_string_literal: true

module LiveEvents
  class GradeChangeEvent < LiveEvents::GradeEvent
    def process
      unless assignment
        Rails.logger.warn("#{event_info} Skipped: assignment not found. Payload: #{payload.inspect}")
        return
      end

      unless user
        Rails.logger.warn("#{event_info} Skipped: user not found. Payload: #{payload.inspect}")
        return
      end

      submission = Submission.where(
        canvas_id: local_canvas_id(payload[:submission_id]),
        canvas_course_id: assignment.course.canvas_id,
        canvas_assignment_id: assignment.canvas_id,
        canvas_user_id: user.canvas_id
      ).first_or_initialize

      submission.points_possible = payload[:points_possible]&.dig('numberStr')
      submission.score = payload[:score]&.dig('numberStr')
      submission.sync_from_api

      Rails.logger.info("#{event_info} Submission synced: submission_id=#{submission.canvas_id}, " \
                        "score=#{submission.score}, points_possible=#{submission.points_possible}")
    end

    private

    def event_info
      @event_info ||= "[Org: #{current_organization.name}] [GradeChangeEvent]"
    end
  end
end
