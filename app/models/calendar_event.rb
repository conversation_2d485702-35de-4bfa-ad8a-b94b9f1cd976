# frozen_string_literal: true

class CalendarEvent < ApplicationRecord
  belongs_to :user, primary_key: :canvas_id, foreign_key: :canvas_user_id

  # HACK: To support Cross Shard updates
  # Returns the canvas_user_id as a string.
  # This method overrides the default getter for canvas_user_id,
  # ensuring that the ID is always returned as a string rather than
  # its native database type (likely an integer).
  def canvas_user_id
    super.to_s
  end

  # Helper methods to interact with sharded & non-sharded calendar events

  after_initialize do
    @record_tenant = Apartment::Tenant.current
  end

  def home_organization
    if canvas_id.to_i < PandaPal::Organization::SHARD_OFFSET
      PandaPal::Organization.for_apt_tenant(@record_tenant)
    else
      PandaPal::Organization.for_canvas_shard(canvas_id)
    end
  end

  def primary_record
    home_organization.switch_tenant do
      self.class.find_by(canvas_id: canvas_id.to_i % PandaPal::Organization::SHARD_OFFSET)
    end
  end

  def sharded_canvas_id
    if canvas_id.to_i < PandaPal::Organization::SHARD_OFFSET
      (PandaPal::Organization::SHARD_OFFSET * home_organization.canvas_shard_id) + canvas_id.to_i
    else
      canvas_id
    end
  end

  def shadow_record?
    canvas_id.to_i > PandaPal::Organization::SHARD_OFFSET
  end

  def context_user_id
    return unless context_code&.start_with?('user_')

    context_code.split('_').last.to_i
  end

  def context_course_id
    return unless context_code&.start_with?('course_')

    context_code.split('_').last.to_i
  end

  def html_path(organization = current_organization)
    event_canvas_id = sharded_id_for_organization(canvas_id.to_i, organization)

    if context_user_id
      user_canvas_id = sharded_id_for_organization(context_user_id, organization)
      "users/#{user_canvas_id}/calendar_events/#{event_canvas_id}"
    elsif context_course_id
      course_canvas_id = sharded_id_for_organization(context_course_id, organization)
      "courses/#{course_canvas_id}/calendar_events/#{event_canvas_id}"
    else
      return nil unless html_url

      begin
        # Extract path from the existing html_url
        uri = URI.parse(html_url)
        uri.path
      rescue StandardError => e
        Rails.logger.error("Error parsing html_url: #{e.message}")
        nil
      end
    end
  end

  private

  def sharded_id_for_organization(id, organization)
    return id if id >= PandaPal::Organization::SHARD_OFFSET
    return id unless organization.canvas_shard_id && organization.canvas_shard_id != 1

    (PandaPal::Organization::SHARD_OFFSET * organization.canvas_shard_id) + id
  end
end
