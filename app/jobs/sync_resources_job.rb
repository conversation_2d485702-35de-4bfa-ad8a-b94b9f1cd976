# frozen_string_literal: true

class SyncResourcesJob < ApplicationJob
  FILES = {
    courses: 'classes.csv',
    school_blackout_dates: 'school_blackout_dates.csv',
    users: 'users.csv'
  }.freeze

  def perform
    return Rails.logger.error("SFTP settings not configured for #{current_organization.name}") unless current_organization.settings[:sftp].present?

    FILES.each do |key, file_name|
      Rails.logger.info "Fetching file #{file_name}"
      SftpClient.new.download_file(file_name)
      Rails.logger.info 'Got CSV, saving...'
      processor = process_csv(key, "/tmp/#{file_name}")
      if processor.success?
        Rails.logger.info "Finished processing #{file_name}"
        # TODO: Remove file from temp folder after it has been processed
        # TODO: Remove file from server after it has been processed
      else
        Rails.logger.error "Error processing file #{file_name}: #{processor.rows_with_errors}"
        SyncResourcesMailer.ftp_import_failed(file_name, processor.rows_with_errors).deliver_later
      end
    rescue StandardError => e # TODO: For now rescue specific errors like FileNotFound or InvalidCSV
      Rails.logger.error "Error processing file #{file_name}: #{e.inspect}"
    end

    # Push Data to Hosted Data to be used in the Snowflake
    HostedDataPushJob.perform_later
  end

  private

  def process_csv(key, file_path)
    case key
    when :courses
      CsvProcessor::CourseImporter.call(file_path, key, Course)
    when :school_blackout_dates
      CsvProcessor::ExceptionDateImporter.call(file_path, key, ExceptionDate)
    when :users
      CsvProcessor::UserImporter.call(file_path, key, User)
    end
  end
end
