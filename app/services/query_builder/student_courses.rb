# frozen_string_literal: true

module QueryBuilder
  class StudentCourses < BaseQueryBuilder
    def self.across_shard(student)
      all_courses = []
      student.against_shards do |shard_student|
        courses = StudentCourses.new({ canvas_user_id: shard_student.canvas_id, organization: current_organization }).records
        all_courses += courses
      end
      all_courses
    end

    def records
      uniq_enrollment_ids = student_uniq_course_enrollment_ids.map(&:canvas_id)
      canvas_course_ids = student_uniq_course_enrollment_ids.map(&:canvas_course_id)
      Enrollment.where(canvas_id: uniq_enrollment_ids)
                .joins(sql_joins(canvas_course_ids))
                .left_outer_joins(:course, :score)
                .where("courses.workflow_state IN ('active', 'completed')")
                .select(sql_selects)
                .order('courses.name ASC')
    end

    def sql_joins(canvas_course_ids)
      <<~SQL.squish
        LEFT JOIN (#{user_courses_with_requirement_counts(canvas_course_ids)}) req_counts ON req_counts.canvas_course_id = enrollments.canvas_course_id
      SQL
    end

    def sql_selects
      <<~SQL.squish
        courses.id AS id,
        courses.canvas_id AS canvas_course_id,
        courses.name AS name,
        courses.sis_id AS course_sis_id,
        courses.course_code AS course_code,
        courses.workflow_state AS course_state,
        courses.image_url AS course_image_url,
        courses.grade_level AS grade_level,
        enrollments.canvas_id AS canvas_enrollment_id,
        enrollments.canvas_user_id AS canvas_user_id,
        enrollments.workflow_state AS enrollment_state,
        scores.current_score AS current_score,
        scores.current_letter_grade AS current_letter_grade,
        req_counts.requirement_count AS requirement_count,
        req_counts.requirement_completed_count AS requirement_completed_count,
        '#{organization_id}' AS organization_id,
        '#{organization_name}' AS organization_name,
        #{organization_shard_id} AS organization_shard_id
      SQL
    end

    def student_uniq_course_enrollment_ids
      Enrollment.students.not_deleted
                .group('enrollments.canvas_course_id')
                .select('min(canvas_id) as canvas_id, canvas_course_id')
                .where(canvas_user_id: user_id)
    end
  end
end
