# frozen_string_literal: true

FactoryBot.define do
  factory :organization, class: 'PandaPal::Organization' do
    name { 'test' }
    key { SecureRandom.hex(16) }
    secret { SecureRandom.hex(16) }
    sequence(:canvas_account_id) { |n| n }
    salesforce_id { Faker::Number.number(digits: 8) }
    settings do
      {
        canvas: {
          base_url: 'https://example.com',
          api_token: 'dummy_token',
          default_time_zone: 'America/Los_Angeles'
        },
        sftp: {
          file_path: 'file_path',
          host: 'host',
          user: 'user',
          key: 'key'
        },
        sftp_endpoints: [
          {
            file_path: 'file_path',
            host: 'host',
            user: 'user',
            password: 'password'
          }
        ],
        instance_names: []
      }
    end
  end
end
