import { useEffect, useState } from 'react';
import { useParams } from "react-router";
import { Link as RouterLink, useSearchParams } from "react-router-dom";

import { Breadcrumb } from '@instructure/ui-breadcrumb';
import { View } from '@instructure/ui-view';

import * as API from "../../utils/api";
import CourseProgress from './CourseProgress';

const CourseNavigation = (props) => {
  const { studentId, courseId } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const orgShardId = searchParams.get('orgShardId');

  const CourseBreadCrumb = () => {
    return (
      <Breadcrumb label="" size="small" margin="small">
         <Breadcrumb.Link>
           <RouterLink to="/">My Courses</RouterLink>
         </Breadcrumb.Link>
         <Breadcrumb.Link>Course:</Breadcrumb.Link>
      </Breadcrumb>
    )
  }

  return(
    <View className='header-text'>
      <CourseBreadCrumb />
      <CourseProgress studentId={studentId} courseId={courseId} orgShardId={orgShardId} />
    </View>
  )
}

export default CourseNavigation
