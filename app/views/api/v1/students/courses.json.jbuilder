# frozen_string_literal: true

json.courses @records do |rec|
  json.id                           rec.id
  json.canvas_course_id             rec.canvas_course_id
  json.name                         rec.name
  json.course_sis_id                rec.course_sis_id
  json.course_code                  rec.course_code
  json.course_state                 rec.course_state
  json.course_image_url             rec.course_image_url
  json.canvas_enrollment_id         rec.canvas_enrollment_id
  json.canvas_user_id               rec.canvas_user_id
  json.enrollment_state             rec.enrollment_state
  json.grade_level                  rec.grade_level
  json.current_score                rec.current_score
  json.current_letter_grade         rec.current_letter_grade
  json.requirement_count            rec.requirement_count
  json.requirement_completed_count  rec.requirement_completed_count
  json.organization_id              rec.organization_id
  json.organization_name            rec.organization_name
  json.organization_shard_id        rec.organization_shard_id
end

json.default_image_url @default_course_image&.file&.url
