# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiveEvents::GradeEvent, type: :service do
  let(:assignment) { create(:assignment, canvas_id: 34) }
  let(:user) { create(:user, canvas_id: 56) }
  let(:submission) { create(:submission, canvas_id: 78, canvas_assignment_id: assignment.canvas_id, canvas_user_id: user.canvas_id) }

  let(:payload) do
    {
      body: {
        submission_id: submission.canvas_id,
        assignment_id: assignment.canvas_id,
        user_id: user.canvas_id
      }
    }.with_indifferent_access
  end

  let(:service) { described_class.new(payload) }

  before do
    # Set the payload instance variable to simulate what BaseHandler does
    service.instance_variable_set(:@payload, payload[:body])
  end

  describe '#process' do
    it 'raises NotImplementedError' do
      expect { service.process }.to raise_error('process must be implemented in your subclass')
    end
  end

  describe '#submission' do
    it 'finds submission by canvas_id' do
      expect(service.send(:submission)).to eq(submission)
    end

    it 'returns nil when submission not found' do
      submission.destroy
      expect(service.send(:submission)).to be_nil
    end

    it 'handles cross-shard IDs' do
      global_id = 10_000_000_000_078
      payload[:body][:submission_id] = global_id

      expect(service.send(:submission)).to eq(submission)
    end
  end

  describe '#assignment' do
    it 'finds assignment by canvas_id' do
      expect(service.send(:assignment)).to eq(assignment)
    end

    it 'returns nil when assignment not found' do
      assignment.destroy
      expect(service.send(:assignment)).to be_nil
    end

    it 'handles cross-shard IDs' do
      global_id = 10_000_000_000_034
      payload[:body][:assignment_id] = global_id

      expect(service.send(:assignment)).to eq(assignment)
    end
  end

  describe '#user' do
    it 'finds user by canvas_id' do
      expect(service.send(:user)).to eq(user)
    end

    it 'returns nil when user not found' do
      user.destroy
      expect(service.send(:user)).to be_nil
    end

    it 'handles cross-shard IDs' do
      global_id = 10_000_000_000_056
      payload[:body][:user_id] = global_id

      expect(service.send(:user)).to eq(user)
    end
  end

  describe '#local_canvas_id' do
    it 'converts global ID to local ID' do
      global_id = 10_000_000_000_123
      local_id = service.send(:local_canvas_id, global_id)

      expect(local_id).to eq(123)
    end

    it 'handles already local IDs' do
      local_id = 123
      result = service.send(:local_canvas_id, local_id)

      expect(result).to eq(123)
    end

    it 'handles string IDs' do
      global_id = '10000000000123'
      local_id = service.send(:local_canvas_id, global_id)

      expect(local_id).to eq(123)
    end
  end
end
