# frozen_string_literal: true

class LtiController < ApplicationController
  skip_authorization_check

  # TODO: might be tweak the render component if UI is differ
  def course_navigation
    authorize! :launch_from, :course

    js_env(
      user: current_user,
      user_config: current_user.user_config,
      canvas_user_timezone: current_session.custom_lti_params[:canvas_user_timezone],
      canvas_user_locale: current_session.custom_lti_params[:canvas_user_locale],
      context: 'course',
      canvas_course_id: current_session.custom_lti_params[:canvas_course_id],
      canvas_shard_id: current_session.custom_lti_params[:canvas_shard_id],
      blackout_dates: ExceptionDate.pluck(:date)
    )

    if current_ability.learning_coach?
      render component: 'LearningCoachDashboard', prerender: false
    elsif current_ability.k5_student?
      render component: 'StudentDashboard', prerender: false
    end
  end

  def global_navigation
    authorize! :launch_from, current_ability.launch_context

    js_env(
      user: current_user,
      user_config: current_user.user_config,
      canvas_user_timezone: current_session.custom_lti_params[:canvas_user_timezone],
      canvas_user_locale: current_session.custom_lti_params[:canvas_user_locale],
      canvas_shard_id: current_session.custom_lti_params[:canvas_shard_id],
      canvas_observee_ids: current_session.custom_lti_params[:canvas_observee_ids],
      assignment_course_id: current_session.custom_lti_params[:canvas_course_id],
      blackout_dates: ExceptionDate.pluck(:date)
    )

    if current_ability.learning_coach?
      render component: 'LearningCoachDashboard', prerender: false
    elsif current_ability.k5_student?
      render component: 'StudentDashboard', prerender: false
    end
  end

  def account_navigation
    authorize! :launch_from, :account

    js_env(
      user: current_user,
      user_config: current_user.user_config,
      canvas_user_timezone: current_session.custom_lti_params[:canvas_user_timezone],
      canvas_user_locale: current_session.custom_lti_params[:canvas_user_locale],
      canvas_account_id: current_session.custom_lti_params[:canvas_account_id],
      canvas_shard_id: current_session.custom_lti_params[:canvas_shard_id],
      blackout_dates: ExceptionDate.pluck(:date)
    )

    render component: 'AccountAdminDashboard', prerender: false
  end
end
