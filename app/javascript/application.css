.ic-Layout-wrapper {
  max-width: 100% !important;
}

.empty-border-box {
  content: "";
  height: 3.5rem;
  width: 0.1rem;
  background: rgb(16, 16, 16);
  display: block;
  margin-left: 5px;
  margin-right: 5px;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    /* background-color: #F8F8FF; */
    font-family: 'Balsamiq Sans', cursive;
    width: 100%;
    overflow-x: hidden;
}

[data-react-class]
  {height: 100% !important;}

.container-with-bg {
  background-color: #DFEBFB !important;
}


.css-1j1gydm-view-panel__content {
  background-color: transparent !important;
}

.full-window {
  height: 100% !important;
  min-height: 100vh;
  position: absolute;
  left: 0;
  width: 100%;
  background-color: lightblue;
}

#theme-selection {
  .selected-icon-border {
    border-width: 2px;
    border-style: solid;
    border-color: #1548E6;
    border-radius: 12px;
    display: inline-flex;
    padding: 0;
    margin: 0;
    line-height: 0;
    overflow: hidden;
  }
}

.white-text{
  color: white !important;
  fill: white !important;
}

.theme-space .header-text
 {
  color: #D1D1D1 !important;
  fill: #D1D1D1 !important;
}

.theme-elements-wrapper img{
  max-width: 700px;
}

.dashboard-theme-winter{
  background-color: #73C2EF !important;
}

.dashboard-theme-forest{
  background-color: #d9f1fd !important;
}

.dashboard-theme-space{
  background-color: #0e1d30 !important;
}

.dashboard-theme-no_theme{
  background-color: #F5F5F5 !important;
}

.renderBubbleMessage {
  position: absolute;
  right: 0;
  top: 200px;
}

.bubble-message {
	position: absolute;
	background: #fff;
	border: 1px solid #000;
  /* bottom: 433px; */
  /* left: 230px; */
  border-radius: 6px !important;
  padding: 12px 15px;
  text-align: left;
  line-height: 19px;
  max-width: 168px !important;
  font-size: 18px;
}
.bubble-message:after, .bubble-message:before {
	top: 100%;
	left: 20px;
	border: solid transparent;
	content: "";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
}

.bubble-message:after {
	border-color: rgba(255, 255, 255, 0);
	border-top-color: #fff;
	border-width: 7px;
	margin-left: -7px;
}
.bubble-message:before {
	border-color: rgba(0, 0, 0, 0);
	border-top-color: #000;
	border-width: 8px;
	margin-left: -8px;
}

#agenda_view div[role="tabpanel"] {
  box-shadow: 0px 2px 8px rgba(218, 11, 11, 0.18);
  overflow: hidden;
  border-style: solid;
  border-color: #FFFFFF;
  background-color: #FFFFFF;
  border-radius: 0px 6px 6px 6px;
}

#progress-bar {
  position: relative;
}

#completion-art {
  position: absolute;
  right: 87px;
}

#progress-bar-value span span span {
  background: linear-gradient(to right, #1548E6, #62C7FF 100%);
}
