# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiveEvents::ModuleEvent, type: :service do
  let(:course) { create(:course, canvas_id: 12) }

  let(:payload) do
    {
      body: {
        context_type: 'Course',
        context_id: course.canvas_id,
        module_id: 34,
        workflow_state: 'active'
      }
    }.with_indifferent_access
  end

  let(:service) { described_class.new(payload) }

  before do
    allow(Rails.logger).to receive(:info)
  end

  describe '#process' do
    context 'when context_type is not Course' do
      before { payload[:body][:context_type] = 'Group' }

      it 'does not process the event' do
        expect(ContextModule).not_to receive(:find_or_initialize_by)
        service.perform(payload)
      end
    end

    context 'when context_type is Course' do
      let(:context_module_double) { instance_double(ContextModule, sync_from_api: true, canvas_id: 34, canvas_context_id: 12, workflow_state: 'active') }
      let(:course_double) { instance_double(Course, sync_from_api: true, canvas_id: 12, new_record?: false) }

      before do
        allow(ContextModule).to receive(:find_or_initialize_by).and_return(context_module_double)
        allow(Course).to receive(:where).and_return(double(first_or_initialize: course_double))
        allow(context_module_double).to receive(:workflow_state=)
      end

      it 'finds or initializes context module with correct attributes' do
        expect(ContextModule).to receive(:find_or_initialize_by).with(
          canvas_id: 34,
          canvas_context_id: course.canvas_id,
          canvas_context_type: 'Course'
        )

        service.perform(payload)
      end

      it 'sets workflow_state when module_item_id is nil' do
        expect(context_module_double).to receive(:workflow_state=).with('active')

        service.perform(payload)
      end

      it 'does not set workflow_state when module_item_id is present' do
        payload[:body][:module_item_id] = 123

        expect(context_module_double).not_to receive(:workflow_state=)

        service.perform(payload)
      end

      it 'syncs context module from API' do
        expect(context_module_double).to receive(:sync_from_api)

        service.perform(payload)
      end

      it 'logs module sync' do
        expect(Rails.logger).to receive(:info).with(/Module synced/)

        service.perform(payload)
      end

      context 'with cross-shard IDs' do
        let(:global_module_id) { 10_000_000_000_034 }
        let(:global_context_id) { 10_000_000_000_012 }

        let(:payload) do
          {
            body: {
              context_type: 'Course',
              context_id: global_context_id,
              module_id: global_module_id,
              workflow_state: 'active'
            }
          }.with_indifferent_access
        end

        it 'converts global IDs to local IDs' do
          expect(ContextModule).to receive(:find_or_initialize_by).with(
            canvas_id: 34, # local ID
            canvas_context_id: 12, # local ID from ensure_course
            canvas_context_type: 'Course'
          )

          service.perform(payload)
        end
      end
    end
  end

  describe '#ensure_course' do
    let(:course_double) { instance_double(Course, sync_from_api: true, canvas_id: 12, new_record?: false) }

    before do
      allow(Course).to receive(:where).and_return(double(first_or_initialize: course_double))
      # Set the payload instance variable to simulate what BaseHandler does
      service.instance_variable_set(:@payload, payload[:body])
    end

    it 'finds or initializes course with local canvas_id' do
      expect(Course).to receive(:where).with(canvas_id: 12)

      service.send(:ensure_course)
    end

    it 'returns existing course without syncing' do
      result = service.send(:ensure_course)

      expect(result).to eq(course_double)
      expect(course_double).not_to receive(:sync_from_api)
    end

    context 'when course is new record' do
      before do
        allow(course_double).to receive(:new_record?).and_return(true)
      end

      it 'syncs new course from API' do
        expect(course_double).to receive(:sync_from_api)

        service.send(:ensure_course)
      end
    end
  end

  describe '#local_canvas_id' do
    it 'converts global ID to local ID' do
      global_id = 10_000_000_000_123
      local_id = service.send(:local_canvas_id, global_id)

      expect(local_id).to eq(123)
    end

    it 'handles already local IDs' do
      local_id = 123
      result = service.send(:local_canvas_id, local_id)

      expect(result).to eq(123)
    end

    it 'handles string IDs' do
      global_id = '10000000000123'
      local_id = service.send(:local_canvas_id, global_id)

      expect(local_id).to eq(123)
    end
  end

  describe '#event_info' do
    it 'returns formatted event info with organization name' do
      expect(service.send(:event_info)).to eq("[Org: #{service.current_organization.name}] [ModuleEvent]")
    end
  end
end
