# frozen_string_literal: true

class ApplicationController < ActionController::Base
  protect_from_forgery with: :exception
  prepend_before_action :forbid_access_if_lacking_session
  check_authorization

  around_action :set_time_zone

  rescue_from CanCan::AccessDenied, Miscellany::HttpErrorHandling::UnauthorizedError do |_e|
    respond_to do |format|
      format.html { render file: 'public/401.html', status: :unauthorized }
      format.json do
        render json: { message: 'You are not authorized to view this page' }, status: 401
      end
    end
  end

  unless Rails.env.production?
    before_action do
      ActiveStorage::Current.url_options = { protocol: request.protocol, host: request.host, port: request.port }
    end
  end

  def js_env(opts = {})
    if @js_env.nil?
      @js_env = {}
      @js_env[:params] = params.except('action', 'controller')
      @js_env[:csrf_token] = form_authenticity_token
      @js_env[:session_key] = current_session.session_key
      @js_env[:organization_id] = current_organization.id
      @js_env[:base_path] = url_for(path: nil, only_path: true)
      @js_env[:canvas_url] = current_organization.settings[:canvas][:base_url]
      @js_env[:environment] = Rails.env
      @js_env[:launch_params] = current_session_data[:launch_params] if current_session_data[:launch_params].present?
      @js_env[:current_time_zone] = current_time_zone
    end
    @js_env.deep_merge! opts
    @js_env
  end
  helper_method :js_env

  def current_user
    @current_user ||= User.find_or_initialize_by(canvas_id: current_session.get_lti_cust_param('custom_canvas_user_id'))
  end

  def current_ability
    @current_ability ||= Ability.new(current_user, rails_session: session, panda_session: panda_session)
  end

  def panda_session
    current_session(create_missing: false)
  end

  def current_time_zone
    @current_time_zone ||= current_session&.custom_lti_params&.dig(:canvas_user_timezone) || 'UTC'
  end

  private

  def set_time_zone(&)
    Time.use_zone(current_time_zone, &)
  end

  # HACK: Compare canvas users from different shards
  def same_user?(user1, user2)
    user1.sharded_canvas_id.to_s == user2.sharded_canvas_id.to_s
  end

  # HACK: To support Cross Shard updates
  # Executes the provided block within the context of the specified organization's tenant.
  # If an organization name is provided, it will switch to that organization's tenant before
  # executing the block. If no organization name is provided, it simply executes the block
  # in the current tenant context.
  #
  # @param org_name [String, nil] The name of the organization to switch to, or nil to use current tenant
  # @yield The block to be executed in the organization's tenant context
  # @return [Object] The result of the block execution
  def with_organization(org_name, &)
    if org_name
      PandaPal::Organization.find_by_name!(org_name).switch_tenant(&)
    else
      yield
    end
  end
end
