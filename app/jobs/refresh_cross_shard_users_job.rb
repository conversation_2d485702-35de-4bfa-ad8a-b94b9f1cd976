# frozen_string_literal: true

class RefreshCrossShardUsersJob < ApplicationJob
  queue_as :default

  def perform(udated_after: nil)
    updated_after ||= batch_context[:updated_after]
    local_shard = current_organization.canvas_shard_id

    users = User.where('canvas_id >= ?', PandaPal::Organization::SHARD_OFFSET)
    users = users.where('updated_at >= ?', updated_after) if updated_after.present?

    sharded_ids = users.pluck(:canvas_id)
    ids_by_origin_shard = sharded_ids.group_by { |id| (id.to_i / PandaPal::Organization::SHARD_OFFSET).floor }

    ids_by_origin_shard.each do |origin_shard, ids|
      ids = ids.map { |id| id % PandaPal::Organization::SHARD_OFFSET }
      origin_shard_org = PandaPal::Organization.for_canvas_shard(origin_shard)
      next unless origin_shard_org

      origin_shard_org.switch_tenant do
        # Add local_shard to each User (ids) in the origin_shard
        UserShardAssociation.import(
          ids.map { |id| { canvas_user_id: id, canvas_shard_id: local_shard } },
          validate: false,
          on_duplicate_key_update: {
            conflict_target: [:canvas_user_id, :canvas_shard_id]
          }
        )
      end
    end

    # TODO: In the local_shard, verify each User's list of other shards, removing extra ones.
    #   This should be a _very_ rare case, and really the list can contain extraneous values - removing
    #   such is just a performance optimization of queries that have to run on each shard.
  end
end
