# frozen_string_literal: true

require 'zip'

class ZipExtractor
  def self.extract(zip_path, destination)
    extracted_files = []

    Zip::File.open(zip_path) do |zip_file|
      zip_file.each do |entry|
        next unless entry.name.end_with?('.csv')

        csv_path = destination.join(entry.name)
        entry.extract(csv_path.to_s) { true }
        extracted_files << csv_path.to_s
        Rails.logger.info("Extracted: #{entry.name}")
      end
    end

    extracted_files
  end
end
