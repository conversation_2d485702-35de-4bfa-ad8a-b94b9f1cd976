# frozen_string_literal: true

class UserConfig < ApplicationRecord
  enum :home_page, { course_view: 0, agenda_view: 1 }
  enum :theme, { no_theme: 0, forest: 1, winter: 2, space: 3 }, default: :forest

  validates :canvas_user_id, uniqueness: true, presence: true

  belongs_to :user, primary_key: :canvas_id, foreign_key: :canvas_user_id
  after_initialize :set_defaults

  def set_defaults
    self.home_page ||= :course_view
  end
end
