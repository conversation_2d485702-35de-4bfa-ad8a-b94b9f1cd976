/* Theme Wrapper Styles */
.theme-wrapper {
  position: relative;
  min-height: 100vh;
  padding: 0;
  margin: 0;
  transition: all 0.3s ease-in-out;
  width: 100vw;
  overflow-x: hidden;
  /* Default background attachment for desktop */
  background-attachment: fixed;
}

/* Base theme variables - can be overridden by specific themes */
:root {
  --theme-transition-duration: 0.3s;
  --theme-border-radius: 8px;
  --theme-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --theme-backdrop-blur: blur(5px);
}

/* Forest Theme */
.theme-forest {
  --theme-primary: #2D5016;
  --theme-secondary: #4A7C59;
  --theme-accent: #8FBC8F;
  --theme-background: #F0F8E8;
  --theme-text: #1F2937;
  --theme-text-light: #374151;
}

.theme-forest .dashboard-body-container {
  background-color: rgba(240, 248, 232, 0.9);
}

.theme-forest .theme-element {
  position: absolute;
  pointer-events: none;
  z-index: 1;
}

.theme-forest .theme-cloud {
  animation: float 6s ease-in-out infinite;
}

.theme-forest .theme-cloud:nth-child(2) {
  animation-delay: -2s;
}

.theme-forest .theme-cloud:nth-child(3) {
  animation-delay: -4s;
}

.theme-forest .theme-sun {
  animation: rotate 20s linear infinite;
}

.theme-element.theme-image-bottom {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 1;
}

/* Winter Theme */
.theme-winter {
  --theme-primary: #1E3A8A;
  --theme-secondary: #3B82F6;
  --theme-accent: #93C5FD;
  --theme-background: #F0F9FF;
  --theme-text: #1F2937;
  --theme-text-light: #374151;
}

.theme-winter .dashboard-body-container {
  background-color: rgba(240, 249, 255, 0.9);
}

/* Space Theme */
.theme-space {
  --theme-primary: #4C1D95;
  --theme-secondary: #7C3AED;
  --theme-accent: #C4B5FD;
  --theme-background: #F5F3FF;
  --theme-text: #1F2937;
  --theme-text-light: #374151;
}

.theme-space .dashboard-body-container {
  background-color: rgba(245, 243, 255, 0.9);
}

/* No Theme (Default) */
.theme-no_theme {
  --theme-primary: #374151;
  --theme-secondary: #6B7280;
  --theme-accent: #9CA3AF;
  --theme-background: #F9FAFB;
  --theme-text: #1F2937;
  --theme-text-light: #374151;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-wrapper {
    /* Use scroll instead of fixed for better mobile performance */
    background-attachment: scroll;
    /* Ensure background covers the viewport properly on mobile */
    background-size: cover;
    background-position: center;
  }

  .theme-element {
    display: none;
  }
}

/* Additional mobile optimizations */
@media (max-width: 480px) {
  .theme-wrapper {
    /* Further optimize for small screens */
    background-size: cover;
    min-height: 100vh;
  }

  .theme-wrapper .dashboard-body-container {
    margin: 8px;
    padding: 12px;
  }
}

/* Theme-aware component overrides */
.theme-wrapper .dashboard-body-container {
  backdrop-filter: var(--theme-backdrop-blur);
  border-radius: var(--theme-border-radius);
  margin: 16px;
  padding: 16px;
  box-shadow: var(--theme-shadow);
  transition: all var(--theme-transition-duration) ease-in-out;
}

/* Ensure text readability across all themes */
.theme-wrapper .dashboard-body-container * {
  color: var(--theme-text);
}

.theme-wrapper .dashboard-body-container .secondary-text {
  color: var(--theme-text-light);
}

/* Theme-specific tab styling */
.theme-wrapper .dashboard-body-container .tabs {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: var(--theme-border-radius);
  transition: all var(--theme-transition-duration) ease-in-out;
}

.theme-wrapper .dashboard-body-container .tab-active {
  background-color: var(--theme-primary);
  color: white;
  transition: all var(--theme-transition-duration) ease-in-out;
}

.theme-wrapper .dashboard-body-container .tab-inactive {
  background-color: var(--theme-background);
  color: var(--theme-text);
  transition: all var(--theme-transition-duration) ease-in-out;
}
