# frozen_string_literal: true

class SyncDataFromSftpJob < ApplicationJob
  FILES = {
    courses: 'classes.csv',
    users: 'users.csv'
  }.freeze

  LOCAL_DIR = Rails.root.join('tmp/sftp_downloads')
  BATCH_SIZE = 1000

  def perform
    return Rails.logger.error("SFTP settings not configured for #{current_organization.name}") if sftp_endpoints.blank?

    sftp_endpoints.each { |endpoint| process_sftp_endpoint(endpoint) }
  end

  private

  def sftp_endpoints
    current_organization.settings[:sftp_endpoints]
  end

  def instance_names
    current_organization.settings[:instance_names] || []
  end

  def process_sftp_endpoint(endpoint)
    Rails.logger.info("Connecting to SFTP: #{endpoint[:host]}")

    SftpClientV2.new(endpoint, instance_names).fetch_files.each do |zip_file|
      process_zip(zip_file, endpoint[:host], endpoint[:user])
    rescue StandardError => e
      Rails.logger.error("Error processing SFTP Files #{endpoint[:host]}: #{e.inspect}")
      raise e
    end
  end

  def process_zip(zip_file, server_name, user_name)
    destination_dir = LOCAL_DIR.join(current_organization.name, server_name, user_name)

    Rails.logger.info("Processing ZIP from #{server_name}: #{zip_file}")
    ZipExtractor.extract(zip_file, destination_dir).each do |csv_file|
      process_csv(csv_file) if FILES.value?(File.basename(csv_file))
    ensure
      delete_file(csv_file)
    end

    delete_file(zip_file)
  end

  def delete_file(path)
    FileUtils.rm_f(path)
  end

  def process_csv(csv_file)
    key = FILES.key(File.basename(csv_file))
    Rails.logger.info("Processing CSV: #{csv_file}")

    send("process_#{key}_csv", csv_file) if key
  end

  def process_users_csv(csv_file)
    process_csv_batch(csv_file) do |rows|
      sis_data_map = rows.index_by { |row| row['identifier'] }.transform_values do |row|
        { grade_level: parse_grades(row['grades']) }
      end

      update_users(sis_data_map) if sis_data_map.present?
    end
  end

  def process_courses_csv(csv_file)
    process_csv_batch(csv_file) do |rows|
      sis_data_map = rows.index_by { |row| [row['sourcedId'], row['courseSourcedId']] }.transform_values do |row|
        { grade_level: parse_grades(row['grades']) }
      end

      update_courses(sis_data_map) if sis_data_map.present?
    end
  end

  def process_csv_batch(csv_file, &)
    CSV.foreach(csv_file, headers: true).each_slice(BATCH_SIZE, &)
  end

  def parse_grades(grades)
    return nil unless grades.present?

    begin
      JSON.parse(grades).first
    rescue StandardError
      nil
    end
  end

  def update_users(sis_data_map)
    users_to_update = User.joins(:pseudonyms)
                          .where(pseudonyms: { sis_id: sis_data_map.keys })
                          .find_each(batch_size: 500)
                          .map { |user| prepare_user_update(user, sis_data_map) }
                          .compact

    User.import(users_to_update, on_duplicate_key_update: [:grade_level]) if users_to_update.any?
  end

  def prepare_user_update(user, sis_data_map)
    user.pseudonyms.pluck(:sis_id).each do |sis_id|
      data = sis_data_map[sis_id]
      next unless data&.dig(:grade_level).present?

      grade_level = data[:grade_level]
      grade_level = grade_level.to_i.to_s if grade_level.match?(/^\d+$/)

      user.assign_attributes(grade_level: grade_level)
      return user
    end
    nil
  end

  def update_courses(sis_data_map)
    sis_ids = sis_data_map.keys.flatten.uniq
    courses_to_update = Course.where(sis_id: sis_ids).find_each(batch_size: 500)
                              .map { |course| prepare_course_update(course, sis_data_map) }
                              .compact

    Course.import(courses_to_update, on_duplicate_key_update: [:grade_level]) if courses_to_update.any?
  end

  def prepare_course_update(course, sis_data_map)
    sis_id = sis_data_map.keys.find { |ids| ids.include?(course.sis_id) }
    data = sis_data_map[sis_id]
    return nil unless data&.dig(:grade_level).present?

    grade_level = data[:grade_level]
    grade_level = grade_level.to_i.to_s if grade_level.match?(/^\d+$/)

    course.assign_attributes(grade_level: grade_level)
    course
  end
end
