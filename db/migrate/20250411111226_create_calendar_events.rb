class CreateCalendarEvents < ActiveRecord::Migration[7.0]
  def change
    create_table :calendar_events do |t|
      t.bigint :canvas_id, null: false
      t.bigint :canvas_user_id, null: false
      t.string :title
      t.datetime :start_at
      t.datetime :end_at
      t.string :workflow_state
      t.string :context_code
      t.string :context_name
      t.string :location_name
      t.text :description
      t.string :url
      t.string :html_url

      t.timestamps
    end

    add_index :calendar_events, [:canvas_id, :canvas_user_id], unique: true
    add_index :calendar_events, :canvas_user_id
    add_index :calendar_events, :start_at
  end
end
